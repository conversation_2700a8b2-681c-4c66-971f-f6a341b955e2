package org.dromara.wallet.wallet.monitor.tron.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.tron.TronContractModel;
import org.dromara.common.scanning.chain.model.tron.TronLogModel;
import org.dromara.common.scanning.monitor.TronMonitorEvent;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.service.IMetaTrc20CstaddressinfoService;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * TRON地址预过滤事件
 * 负责检查交易是否涉及监控地址，实现早期过滤以提升性能
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>地址预过滤：检查交易是否涉及监控的钱包地址或合约地址</li>
 *   <li>性能优化：早期过滤减少后续不必要的API调用</li>
 *   <li>支持TRC20和TRX原生转账的地址检查</li>
 *   <li>设置处理标记，供后续MonitorEvent参考</li>
 *   <li>地址监控逻辑：集成了从TronTransactionManager移动过来的地址监控功能</li>
 * </ul>
 *
 * <p>重构说明：</p>
 * <ul>
 *   <li>移除对TronTransactionManager的依赖，直接依赖IMetaTrc20CstaddressinfoService</li>
 *   <li>将getMonitoredAddresses和isMonitoredTransaction方法从TronTransactionManager移动到此处</li>
 *   <li>实现自包含的地址监控逻辑，消除循环依赖</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TronAddressFilterEvent implements TronMonitorEvent {

    private final IMetaTrc20CstaddressinfoService tronAddressService;
    private final TronConfigFacade configFacade;



    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 检查是否为TRON交易模型
            if (transactionModel.getTronTransactionModel() == null) {
                log.debug("{}链跳过非TRON交易模型", configFacade.getChainName());
                return;
            }

            // 获取TRON交易模型
            org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel =
                transactionModel.getTronTransactionModel();

            String txId = tronTxModel.getTxID();
            if (txId == null) {
                log.debug("{}链交易ID为空，跳过处理", configFacade.getChainName());
                return;
            }

            // 执行地址预过滤
            boolean shouldProcess = shouldProcessTransaction(tronTxModel);

            if (shouldProcess) {
                log.debug("{}链交易{}通过地址过滤", configFacade.getChainName(), txId);
            } else {
                log.debug("{}链交易{}不涉及监控地址，跳过处理", configFacade.getChainName(), txId);
            }

        } catch (Exception e) {
            log.error("{}链地址过滤处理失败: {}", configFacade.getChainName(), e.getMessage());
            // 异常时不做任何处理，让后续Event自行判断
        }
    }

    /**
     * 检查交易是否需要处理（地址预过滤）
     */
    private boolean shouldProcessTransaction(org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel) {
        // 获取监控地址集合
        Set<String> monitoredContractAddresses = getMonitoredContractAddresses();
        Set<String> monitoredWalletAddresses = getMonitoredWalletAddresses();

        if (monitoredContractAddresses.isEmpty() && monitoredWalletAddresses.isEmpty()) {
            log.debug("{}链没有配置监控地址，跳过交易处理", configFacade.getChainName());
            return false;
        }

        TronContractModel tronContractModel = tronTxModel.getRawData().getContract().get(0);

        // 检查基本的from/to地址
        String fromAddress = tronContractModel.getParameter().getValue().getOwnerAddress();
        String toAddress = tronContractModel.getParameter().getValue().getToAddress();

        // 检查钱包地址
        if (containsMonitoredAddress(fromAddress, monitoredWalletAddresses) ||
            containsMonitoredAddress(toAddress, monitoredWalletAddresses)) {
            return true;
        }

        // 检查合约地址（TRC20转账的to地址通常是合约地址）
        if (containsMonitoredAddress(toAddress, monitoredContractAddresses)) {
            return true;
        }

        // 对于TRC20转账，还需要检查data字段中的接收地址
        // 这里只做基础检查，详细的TRC20解析在后续的Event中进行
        if (tronTxModel.getRawData() != null) {
            // 如果有data字段且to地址是监控的合约，可能是TRC20转账
            if (containsMonitoredAddress(toAddress, monitoredContractAddresses)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查地址是否在监控集合中
     */
    private boolean containsMonitoredAddress(String address, Set<String> monitoredAddresses) {
        if (address == null || monitoredAddresses.isEmpty()) {
            return false;
        }
        return monitoredAddresses.contains(address.toLowerCase());
    }

    /**
     * 获取监控的合约地址集合
     */
    private Set<String> getMonitoredContractAddresses() {
        return configFacade.getEnabledContractAddresses().stream()
            .map(String::toLowerCase)
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 获取监控的钱包地址集合
     * 重构说明：从TronTransactionManager移动到此处，消除依赖
     */
    private Set<String> getMonitoredWalletAddresses() {
        return getMonitoredAddresses(configFacade).stream()
            .map(String::toLowerCase)
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 获取监控地址集合
     * 从TronTransactionManager移动到此处的方法
     *
     * @param configFacade TRON配置门面
     * @return 监控地址集合
     */
    public Set<String> getMonitoredAddresses(TronConfigFacade configFacade) {
        // 获取TRON监控地址集合，用于性能优化
        return TenantHelper.ignore(tronAddressService::queryAllAddressSet);
    }

    /**
     * 检查交易是否涉及监控地址
     * 从TronTransactionManager移动到此处的方法
     * 用于在完整解析前进行快速过滤
     *
     * @param tronLogModel       TRON日志模型
     * @param monitoredAddresses 监控地址集合
     * @param configFacade       TRON配置门面
     * @return 如果交易涉及监控地址则返回true
     */
    public boolean isMonitoredTransaction(TronLogModel tronLogModel, Set<String> monitoredAddresses, TronConfigFacade configFacade) {
        if (monitoredAddresses == null || monitoredAddresses.isEmpty()) {
            return true; // 如果没有监控地址集合，保守处理所有交易
        }

        try {
            // 快速提取地址
            String[] addresses = extractAddressesFromLog(tronLogModel);
            if (addresses == null || addresses.length != 2) {
                return true; // 地址提取失败，保守处理
            }

            String fromAddress = addresses[0];
            String toAddress = addresses[1];

            // 检查是否涉及监控地址
            boolean isMonitored = monitoredAddresses.contains(fromAddress) || monitoredAddresses.contains(toAddress);

            if (log.isTraceEnabled()) {
                log.trace("{}链交易{}地址检查: from={}, to={}, 是否监控={}",
                    configFacade.getChainName(), tronLogModel.getTransactionHash(), fromAddress, toAddress, isMonitored);
            }

            return isMonitored;

        } catch (Exception e) {
            log.debug("{}链交易{}地址检查失败，保守处理: {}",
                configFacade.getChainName(), tronLogModel.getTransactionHash(), e.getMessage());
            return true; // 检查失败，保守处理
        }
    }

    /**
     * 从TronLogModel中快速提取地址
     * 简化版本的地址提取方法
     *
     * @param tronLogModel TRON日志模型
     * @return 地址数组 [fromAddress, toAddress]，失败时返回null
     */
    private String[] extractAddressesFromLog(TronLogModel tronLogModel) {
        try {
            // TRON的Transfer事件结构与EVM类似
            // 从topics中提取from和to地址
            if (tronLogModel.getTopics() == null || tronLogModel.getTopics().size() < 3) {
                return null;
            }

            // 解析from地址
            String fromAddress = tronLogModel.getTopics().get(1);
            // 解析to地址
            String toAddress = tronLogModel.getTopics().get(2);

            // TRON地址格式转换（如果需要）
            fromAddress = convertTronAddress(fromAddress);
            toAddress = convertTronAddress(toAddress);

            return new String[]{fromAddress.toLowerCase(), toAddress.toLowerCase()};

        } catch (Exception e) {
            log.debug("提取TRON Log地址失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转换TRON地址格式
     * TRON地址可能需要特殊的格式转换
     */
    private String convertTronAddress(String address) {
        if (address == null) {
            return null;
        }

        // 如果是十六进制格式，可能需要转换为Base58格式
        // 这里先简单处理，具体转换逻辑可以根据实际需要调整
        if (address.startsWith("0x") && address.length() > 26) {
            // 去掉前缀0x000...，保留地址部分
            return address.substring(26);
        }

        return address;
    }


}
