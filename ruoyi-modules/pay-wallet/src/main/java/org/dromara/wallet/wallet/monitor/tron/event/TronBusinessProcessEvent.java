package org.dromara.wallet.wallet.monitor.tron.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.tron.TronLogModel;
import org.dromara.common.scanning.monitor.TronMonitorEvent;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.domain.bo.MetaTrc20TransactionBo;
import org.dromara.wallet.service.IMetaTrc20CstaddressinfoService;
import org.dromara.wallet.service.IMetaTrc20TransactionService;
import org.dromara.wallet.wallet.helper.TronHelper;
import org.dromara.wallet.wallet.monitor.tron.dto.MyTronTransactionModel;
import org.dromara.wallet.wallet.monitor.tron.event.TronAddressFilterEvent;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Set;

/**
 * TRON业务处理事件
 * 负责处理过滤后的事件日志，执行完整的业务逻辑
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>交易解析：解析TRC20 Transfer事件，提取交易信息</li>
 *   <li>交易分类：根据监控地址分类交易类型（receive/send/collect/unknown）</li>
 *   <li>业务验证：执行最小金额检查和交易可信度验证</li>
 *   <li>数据保存：保存交易记录到TRON数据库表</li>
 *   <li>错误处理：完善的异常处理和重试机制</li>
 *   <li>性能监控：记录业务处理的性能指标</li>
 * </ul>
 *
 * <p>重构说明：</p>
 * <ul>
 *   <li>移除对TronTransactionManager的依赖，实现自包含的业务处理</li>
 *   <li>将完整的业务处理逻辑从TronTransactionManager移动到此处</li>
 *   <li>包含交易解析、验证、分类、保存等完整功能</li>
 *   <li>消除循环依赖，提高代码的可维护性</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TronBusinessProcessEvent implements TronMonitorEvent {

    // 业务处理相关服务
    private final IMetaTrc20CstaddressinfoService tronAddressService;
    private final IMetaTrc20TransactionService metaTrc20TransactionService;
    private final TronHelper tronHelper;
    private final TronAddressFilterEvent tronAddressFilterEvent;
    private final TronConfigFacade configFacade;

    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 检查是否为TRON交易模型
            if (transactionModel.getTronTransactionModel() == null) {
                log.debug("{}链跳过非TRON交易模型", configFacade.getChainName());
                return;
            }

            String txId = transactionModel.getTronTransactionModel().getTxID();
            if (txId == null) {
                log.debug("{}链交易ID为空，跳过业务处理", configFacade.getChainName());
                return;
            }

            // 获取过滤后的事件日志
            List<TronLogModel> filteredLogs = TronEventFilterEvent.getFilteredLogs(transactionModel);
            if (filteredLogs.isEmpty()) {
                log.debug("{}链交易{}没有过滤后的事件日志，跳过业务处理", configFacade.getChainName(), txId);
                return;
            }

            // 执行业务处理
            processBusinessLogic(filteredLogs, txId);

        } catch (Exception e) {
            log.error("{}链业务处理失败: txID={}, error={}",
                configFacade.getChainName(),
                transactionModel.getTronTransactionModel() != null ?
                    transactionModel.getTronTransactionModel().getTxID() : "unknown",
                e.getMessage());
            // 不重新抛出异常，避免影响其他交易的处理
        }
    }

    /**
     * 执行业务逻辑处理
     * 重构说明：从TronTransactionManager移动过来的完整业务处理逻辑
     */
    private void processBusinessLogic(List<TronLogModel> filteredLogs, String txId) {
        long startTime = System.currentTimeMillis();

        try {
            log.debug("{}链交易{}开始业务处理，事件日志数量: {}",
                configFacade.getChainName(), txId, filteredLogs.size());

            // 处理每个过滤后的日志
            for (TronLogModel logModel : filteredLogs) {
                try {
                    // 1. 解析交易
                    MyTronTransactionModel transaction = parseTransaction(logModel);
                    if (transaction == null) {
                        log.debug("{}链交易{}日志解析失败，跳过处理", configFacade.getChainName(), txId);
                        continue;
                    }

                    // 2. 分类交易类型
                    classifyTransaction(transaction);

                    // 3. 根据交易类型处理
                    switch (transaction.getTransactionType()) {
                        case "receive":
                            handleReceiveTransaction(transaction);
                            break;
                        case "send":
                            handleSendTransaction(transaction);
                            break;
                        case "collect":
                            handleCollectTransaction(transaction);
                            break;
                        case "unknown":
                            log.debug("{}链交易{}类型为unknown，跳过处理",
                                configFacade.getChainName(), transaction.getTxID());
                            break;
                        default:
                            log.debug("{}链交易{}类型未知，跳过处理",
                                configFacade.getChainName(), transaction.getTxID());
                            transaction.setTransactionType("unknown");
                    }

                } catch (Exception e) {
                    log.error("{}链处理交易{}的日志失败: {}",
                        configFacade.getChainName(), txId, e.getMessage());
                    // 继续处理其他日志
                }
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.debug("{}链交易{}业务处理完成，耗时: {}ms",
                configFacade.getChainName(), txId, processingTime);

            // 记录性能指标
            recordPerformanceMetrics(filteredLogs.size(), processingTime);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("{}链交易{}业务处理失败，耗时: {}ms, 错误: {}",
                configFacade.getChainName(), txId, processingTime, e.getMessage());

            // 记录失败指标
            recordFailureMetrics(filteredLogs.size(), processingTime, e);

            // 重新抛出异常，让上层处理
            throw e;
        }
    }

    // ============ 业务处理方法（从TronTransactionManager移动） ============

    /**
     * 解析交易
     * 从TronTransactionManager移动过来的方法
     */
    private MyTronTransactionModel parseTransaction(TronLogModel logModel) {
        try {
            // 创建交易模型
            MyTronTransactionModel transactionModel = new MyTronTransactionModel();

            // 设置基础信息
            transactionModel.setTxID(logModel.getTransactionHash());
            transactionModel.setContractAddress(logModel.getAddress());
            transactionModel.setChainName(configFacade.getChainName());

            // 解析Transfer事件
            parseTransferEvent(transactionModel, logModel);

            log.debug("{}链成功解析Transfer事件: from={}, to={}, contract={}, amount={}, txHash={}",
                configFacade.getChainName(),
                transactionModel.getFromAddress(),
                transactionModel.getToAddress(),
                transactionModel.getContractAddress(),
                transactionModel.getAmount(),
                transactionModel.getTxID());

            return transactionModel;

        } catch (Exception e) {
            log.error("{}链解析交易{}失败: {}", configFacade.getChainName(), logModel.getTransactionHash(), e.getMessage());
            return null;
        }
    }

    /**
     * 解析Transfer事件
     * 从TronTransactionManager移动过来的方法
     */
    private void parseTransferEvent(MyTronTransactionModel transactionModel, TronLogModel logModel) {
        // 设置from和to地址
        transactionModel.setFromAddress(logModel.getFromAddress());
        transactionModel.setToAddress(logModel.getToAddress());

        // 设置原始金额
        transactionModel.setRawAmount(logModel.getAmount());

        // 获取代币信息并转换精度
        String contractAddress = logModel.getAddress();
        String tokenSymbol = getTokenSymbol(contractAddress);
        Integer tokenDecimals = getTokenDecimals(contractAddress);

        transactionModel.setTokenSymbol(tokenSymbol);
        transactionModel.setTokenDecimals(tokenDecimals);

        // 转换金额精度
        if (logModel.getAmount() != null) {
            BigDecimal amount = new BigDecimal(logModel.getAmount())
                .divide(BigDecimal.TEN.pow(tokenDecimals), tokenDecimals, RoundingMode.DOWN);
            transactionModel.setAmount(amount);
        }
    }

    /**
     * 获取代币符号
     * 从TronTransactionManager移动过来的方法
     */
    private String getTokenSymbol(String contractAddress) {
        try {
            // 通过合约地址反查代币符号
            Set<String> enabledTokens = configFacade.getEnabledTokenSymbols();
            for (String tokenSymbol : enabledTokens) {
                String configAddress = configFacade.getContractAddress(tokenSymbol);
                if (contractAddress.equalsIgnoreCase(configAddress)) {
                    return tokenSymbol;
                }
            }
            return "UNKNOWN";
        } catch (Exception e) {
            log.warn("{}链获取合约{}的代币符号失败: {}",
                configFacade.getChainName(), contractAddress, e.getMessage());
            return "UNKNOWN";
        }
    }

    /**
     * 获取代币精度
     * 从TronTransactionManager移动过来的方法
     */
    private Integer getTokenDecimals(String contractAddress) {
        try {
            // 通过合约地址反查代币精度
            String tokenSymbol = getTokenSymbol(contractAddress);
            if (!"UNKNOWN".equals(tokenSymbol)) {
                return configFacade.getTokenDecimals(tokenSymbol);
            }
            return 6; // TRON默认6位精度
        } catch (Exception e) {
            log.warn("{}链获取合约{}的代币精度失败: {}",
                configFacade.getChainName(), contractAddress, e.getMessage());
            return 6; // TRON默认6位精度
        }
    }

    /**
     * 分类交易类型
     * 从TronTransactionManager移动过来的方法
     */
    private void classifyTransaction(MyTronTransactionModel transaction) {
        String fromAddress = transaction.getFromAddress();
        String toAddress = transaction.getToAddress();

        // 获取监控地址列表
        Set<String> monitoredAddresses = TenantHelper.ignore(tronAddressService::queryAllAddressSet);

        if (monitoredAddresses.contains(toAddress)) {
            // 监控地址接收代币
            transaction.setTransactionType("receive");
        } else if (monitoredAddresses.contains(fromAddress)) {
            // 监控地址发送代币
            transaction.setTransactionType("send");
        } else {
            // 非监控地址交易，可能是内部转账或其他
            transaction.setTransactionType("unknown");
        }

        log.debug("{}链交易{}分类为: {}",
            configFacade.getChainName(),
            transaction.getTxID(),
            transaction.getTransactionType());
    }

    /**
     * 处理接收交易
     * 从TronTransactionManager移动过来的方法（简化版本）
     */
    private void handleReceiveTransaction(MyTronTransactionModel transaction) {
        log.info("{}链开始处理接收交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 1. 执行验证逻辑
            if (!checkMinimumDepositAmount(transaction)) {
                // 金额不符合要求，设置为验证失败
                transaction.setProcessStatus(4);
                transaction.setErrorMessage("金额低于最小入账金额");
                saveTransactionRecord(transaction);
                log.info("{}链接收交易验证失败（最小金额）: {}", configFacade.getChainName(), transaction.getTxID());
                return;
            }

            if (!verifyTransactionTrustworthiness(transaction)) {
                // 交易不可信，设置为验证失败
                transaction.setProcessStatus(5);
                transaction.setErrorMessage("交易可信度验证失败");
                saveTransactionRecord(transaction);
                log.info("{}链接收交易验证失败（可信度）: {}", configFacade.getChainName(), transaction.getTxID());
                return;
            }

            // 2. 验证通过，保存交易记录到数据库，设置状态为待处理
            transaction.setProcessStatus(0);
            saveTransactionRecord(transaction);

            log.info("{}链接收交易验证通过，记录保存成功: {}", configFacade.getChainName(), transaction.getTxID());

        } catch (Exception e) {
            log.error("{}链处理接收交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage());

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            throw e;
        }
    }

    /**
     * 处理发送交易
     * 从TronTransactionManager移动过来的方法（简化版本）
     */
    private void handleSendTransaction(MyTronTransactionModel transaction) {
        log.info("{}链处理发送交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 发送交易直接保存记录，设置状态为处理完成
            transaction.setProcessStatus(1);
            saveTransactionRecord(transaction);

            log.info("{}链发送交易记录保存完成: {}", configFacade.getChainName(), transaction.getTxID());

        } catch (Exception e) {
            log.error("{}链处理发送交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage());

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            throw e;
        }
    }

    /**
     * 处理归集交易
     * 从TronTransactionManager移动过来的方法（简化版本）
     */
    private void handleCollectTransaction(MyTronTransactionModel transaction) {
        log.info("{}链处理归集交易: {}", configFacade.getChainName(), transaction.getFormattedInfo());

        try {
            // 归集交易直接保存记录，设置状态为处理完成
            transaction.setProcessStatus(1);
            saveTransactionRecord(transaction);

            log.info("{}链归集交易记录保存完成: {}", configFacade.getChainName(), transaction.getTxID());

        } catch (Exception e) {
            log.error("{}链处理归集交易失败: {}, 错误信息: {}",
                configFacade.getChainName(), transaction.getFormattedInfo(), e.getMessage());

            // 保持待处理状态，等待重试
            transaction.setProcessStatus(0);
            transaction.setErrorMessage(e.getMessage());

            throw e;
        }
    }

    // ============ 验证和保存方法（简化版本） ============

    /**
     * 检查最小入账金额
     * 从TronTransactionManager移动过来的方法（简化版本）
     */
    private boolean checkMinimumDepositAmount(MyTronTransactionModel transaction) {
        try {
            log.debug("{}链检查最小入账金额: txHash={}, amount={}, token={}",
                configFacade.getChainName(), transaction.getTxID(),
                transaction.getAmount(), transaction.getTokenSymbol());

            // 获取交易金额和代币符号
            BigDecimal transactionAmount = transaction.getAmount();
            String tokenSymbol = transaction.getTokenSymbol();

            if (transactionAmount == null || tokenSymbol == null) {
                log.warn("{}链交易{}缺少必要信息: amount={}, token={}",
                    configFacade.getChainName(), transaction.getTxID(),
                    transactionAmount, tokenSymbol);
                return false;
            }

            // 获取最小入账金额配置
            BigDecimal minDepositAmount = configFacade.getMinTransferAmount(tokenSymbol);
            if (minDepositAmount == null) {
                log.warn("{}链无法获取代币{}的最小入账金额配置，跳过验证",
                    configFacade.getChainName(), tokenSymbol);
                return true; // 无配置时默认通过
            }

            // 比较交易金额与最小金额
            boolean isValid = transactionAmount.compareTo(minDepositAmount) >= 0;

            if (!isValid) {
                log.info("{}链交易{}金额过小: {} {} < {} {}",
                    configFacade.getChainName(), transaction.getTxID(),
                    transactionAmount, tokenSymbol, minDepositAmount, tokenSymbol);
            } else {
                log.debug("{}链交易{}金额验证通过: {} {} >= {} {}",
                    configFacade.getChainName(), transaction.getTxID(),
                    transactionAmount, tokenSymbol, minDepositAmount, tokenSymbol);
            }

            return isValid;

        } catch (Exception e) {
            log.error("{}链检查最小入账金额失败: txHash={}, error={}",
                configFacade.getChainName(), transaction.getTxID(), e.getMessage());
            return true; // 出错时默认通过，避免阻塞正常交易
        }
    }

    /**
     * 验证交易可信度
     * 从TronTransactionManager移动过来的方法（简化版本）
     */
    private boolean verifyTransactionTrustworthiness(MyTronTransactionModel transaction) {
        try {
            log.debug("{}链验证交易可信度: txHash={}, to={}, amount={}, token={}",
                configFacade.getChainName(), transaction.getTxID(),
                transaction.getToAddress(), transaction.getAmount(), transaction.getTokenSymbol());

            // 获取交易信息
            String toAddress = transaction.getToAddress();
            BigDecimal transactionAmount = transaction.getAmount();
            String tokenSymbol = transaction.getTokenSymbol();

            if (toAddress == null || transactionAmount == null || tokenSymbol == null) {
                log.warn("{}链交易{}缺少必要信息: to={}, amount={}, token={}",
                    configFacade.getChainName(), transaction.getTxID(),
                    toAddress, transactionAmount, tokenSymbol);
                return false;
            }

            // 查询接收地址的当前余额
            BigDecimal currentBalance = tronHelper.balanceGetForRead(toAddress, tokenSymbol);
            if (currentBalance == null) {
                log.warn("{}链无法查询地址{}的{}余额，可信度验证失败",
                    configFacade.getChainName(), toAddress, tokenSymbol);
                return false;
            }

            // 验证余额是否符合预期：当前余额应该 >= 交易金额
            boolean isTrustworthy = currentBalance.compareTo(transactionAmount) >= 0;

            if (!isTrustworthy) {
                log.warn("{}链交易{}可信度验证失败: 当前余额({} {}) < 交易金额({} {})",
                    configFacade.getChainName(), transaction.getTxID(),
                    currentBalance, tokenSymbol, transactionAmount, tokenSymbol);
            } else {
                log.debug("{}链交易{}可信度验证通过: 当前余额({} {}) >= 交易金额({} {})",
                    configFacade.getChainName(), transaction.getTxID(),
                    currentBalance, tokenSymbol, transactionAmount, tokenSymbol);
            }

            return isTrustworthy;

        } catch (Exception e) {
            log.error("{}链验证交易可信度失败: txHash={}, error={}",
                configFacade.getChainName(), transaction.getTxID(), e.getMessage());
            return true; // 出错时返回true，避免因网络问题等临时错误阻塞正常交易
        }
    }

    /**
     * 保存交易记录到数据库
     * 从TronTransactionManager移动过来的方法（简化版本）
     */
    private void saveTransactionRecord(MyTronTransactionModel transaction) {
        String txHash = transaction.getTxID();

        try {
            log.debug("开始保存{}链交易记录: {}", configFacade.getChainName(), txHash);

            // 转换为TRON业务对象
            MetaTrc20TransactionBo bo = convertToTronBo(transaction);

            // 保存到数据库
            Boolean result = metaTrc20TransactionService.insertByBo(bo);
            if (!Boolean.TRUE.equals(result)) {
                throw new RuntimeException("TRON交易保存失败");
            }

            log.info("{}链交易记录保存成功: txid={}, amount={}, type={}",
                configFacade.getChainName(), txHash, transaction.getAmount(), transaction.getTransactionType());

        } catch (Exception e) {
            log.error("保存{}链交易记录失败: {}, 错误信息: {}", configFacade.getChainName(), txHash, e.getMessage());
            throw new RuntimeException("保存交易记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转换为TRON业务对象
     */
    private MetaTrc20TransactionBo convertToTronBo(MyTronTransactionModel transaction) {
        MetaTrc20TransactionBo bo = new MetaTrc20TransactionBo();

        // 填充字段
        bo.setTxid(transaction.getTxID());
        bo.setAddress(transaction.getToAddress());
        bo.setFromaddress(transaction.getFromAddress());
        bo.setContract(transaction.getContractAddress());
        bo.setAmount(transaction.getAmount());
        bo.setTimestamp(transaction.getTimestamp() != null ? transaction.getTimestamp().longValue() : null);
        bo.setType(transaction.getTransactionType());
        bo.setIssync(transaction.getProcessStatus() != null ? transaction.getProcessStatus().longValue() : null);

        // 构建备注信息
        StringBuilder remark = new StringBuilder();
        if (transaction.getChainName() != null) {
            remark.append("链: ").append(transaction.getChainName());
        }
        if (transaction.getTokenSymbol() != null) {
            if (!remark.isEmpty()) {
                remark.append(", ");
            }
            remark.append("代币: ").append(transaction.getTokenSymbol());
        }
        if (transaction.getErrorMessage() != null && !transaction.getErrorMessage().trim().isEmpty()) {
            if (!remark.isEmpty()) {
                remark.append(", ");
            }
            remark.append("错误: ").append(transaction.getErrorMessage());
        }
        bo.setRemark(!remark.isEmpty() ? remark.toString() : null);

        return bo;
    }

    /**
     * 记录性能指标
     */
    private void recordPerformanceMetrics(int logCount, long processingTime) {
        // 这里可以集成监控系统，记录性能指标
        // 比如Micrometer、Prometheus等
        log.trace("{}链业务处理性能指标 - 日志数量: {}, 处理时间: {}ms",
            configFacade.getChainName(), logCount, processingTime);

        // 如果处理时间过长，记录警告
        if (processingTime > 5000) { // 5秒
            log.warn("{}链业务处理耗时过长: {}ms, 日志数量: {}",
                configFacade.getChainName(), processingTime, logCount);
        }
    }

    /**
     * 记录失败指标
     */
    private void recordFailureMetrics(int logCount, long processingTime, Exception e) {
        // 这里可以集成监控系统，记录失败指标
        log.trace("{}链业务处理失败指标 - 日志数量: {}, 处理时间: {}ms, 异常类型: {}",
            configFacade.getChainName(), logCount, processingTime, e.getClass().getSimpleName());
    }
}
