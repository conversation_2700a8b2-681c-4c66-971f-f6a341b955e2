package org.dromara.wallet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.dromara.wallet.domain.TrcCstaddress;
import org.dromara.wallet.domain.bo.TrcCstaddressBo;
import org.dromara.wallet.domain.vo.TrcCstaddressVo;
import org.dromara.wallet.mapper.TronAddressMapper;
import org.dromara.wallet.service.ITronAddressService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * TRON钱包地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TronAddressServiceImpl implements ITronAddressService {

    private final TronAddressMapper baseMapper;

    /**
     * 查询TRON钱包地址
     *
     * @param id 主键
     * @return TRON钱包地址
     */
    @Override
    public TrcCstaddressVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据客户ID查询TRON钱包地址
     *
     * @param cstId 客户ID
     * @return TRON钱包地址
     */
    @Override
    public TrcCstaddressVo queryByCstId(Long cstId) {
        LambdaQueryWrapper<TrcCstaddress> lqw = Wrappers.lambdaQuery();
        lqw.eq(cstId != null, TrcCstaddress::getCstId, cstId);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 分页查询TRON钱包地址列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return TRON钱包地址分页列表
     */
    @Override
    public TableDataInfo<TrcCstaddressVo> queryPageList(TrcCstaddressBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TrcCstaddress> lqw = buildQueryWrapper(bo);
        Page<TrcCstaddressVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的TRON钱包地址列表
     *
     * @param bo 查询条件
     * @return TRON钱包地址列表
     */
    @Override
    public List<TrcCstaddressVo> queryList(TrcCstaddressBo bo) {
        LambdaQueryWrapper<TrcCstaddress> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建查询条件Wrapper
     */
    private LambdaQueryWrapper<TrcCstaddress> buildQueryWrapper(TrcCstaddressBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TrcCstaddress> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, TrcCstaddress::getId, bo.getId());
        lqw.eq(bo.getCstId() != null, TrcCstaddress::getCstId, bo.getCstId());
        lqw.like(StringUtils.isNotBlank(bo.getCstAddress()), TrcCstaddress::getCstAddress, bo.getCstAddress());
        lqw.like(StringUtils.isNotBlank(bo.getCstHexaddress()), TrcCstaddress::getCstHexaddress, bo.getCstHexaddress());
        return lqw;
    }

    /**
     * 新增TRON钱包地址
     *
     * @param bo TRON钱包地址
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(TrcCstaddressBo bo) {
        TrcCstaddress add = MapstructUtils.convert(bo, TrcCstaddress.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改TRON钱包地址
     *
     * @param bo TRON钱包地址
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(TrcCstaddressBo bo) {
        TrcCstaddress update = MapstructUtils.convert(bo, TrcCstaddress.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 校验并批量删除TRON钱包地址信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 进行业务校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
