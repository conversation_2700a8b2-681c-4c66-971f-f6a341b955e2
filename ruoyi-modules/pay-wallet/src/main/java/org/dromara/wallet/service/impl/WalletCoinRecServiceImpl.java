package org.dromara.wallet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.config.facade.*;
import org.dromara.wallet.domain.WalletCoinRec;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.domain.vo.WalletCoinRecVo;
import org.dromara.wallet.mapper.WalletCoinRecMapper;
import org.dromara.wallet.service.IWalletCoinRecService;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.dromara.wallet.wallet.helper.SolanaHelper;
import org.dromara.wallet.wallet.helper.TronHelper;
import org.dromara.wallet.wallet.monitor.event.BalanceChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 多链用户钱包代币余额记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WalletCoinRecServiceImpl implements IWalletCoinRecService {

    private final WalletCoinRecMapper baseMapper;

    // 扁平化配置（新增）
    private final BscConfigFacade bscConfigFacade;
    private final ArbConfigFacade arbConfigFacade;
    private final BaseConfigFacade baseConfigFacade;
    private final TronConfigFacade tronConfigFacade;
    private final SolanaConfigFacade solanaConfigFacade;

    // Helper类
//    private final TronHelper tronHelper;
    private final TronHelper tronHelper;
    private final EvmHelper evmHelper;
    private final SolanaHelper solanaHelper;

    /**
     * 钱包处理结果统计类
     */
    public static class WalletProcessResult {
        // Getters
        @Getter
        private final String walletAddress;
        private final String chainType;
        private final List<String> successTokens = new ArrayList<>();
        private final List<String> failedTokens = new ArrayList<>();
        private final Map<String, BigDecimal> tokenBalances = new HashMap<>();
        private long startTime;
        private long endTime;

        public WalletProcessResult(String walletAddress, String chainType) {
            this.walletAddress = walletAddress;
            this.chainType = chainType;
            this.startTime = System.currentTimeMillis();
        }

        public void addSuccess(String tokenSymbol, BigDecimal balance) {
            successTokens.add(tokenSymbol);
            tokenBalances.put(tokenSymbol, balance);
        }

        public void addFailure(String tokenSymbol) {
            failedTokens.add(tokenSymbol);
        }

        public void finish() {
            this.endTime = System.currentTimeMillis();
        }

        public String getSummary() {
            long duration = endTime - startTime;
            return String.format("钱包[%s]处理完成 - 链类型:%s, 成功:%d个代币%s, 失败:%d个代币%s, 耗时:%dms",
                walletAddress, chainType,
                successTokens.size(), successTokens.isEmpty() ? "" : successTokens,
                failedTokens.size(), failedTokens.isEmpty() ? "" : failedTokens,
                duration);
        }

        public String getDetailedSummary() {
            StringBuilder sb = new StringBuilder();
            sb.append(getSummary()).append("\n");
            if (!tokenBalances.isEmpty()) {
                sb.append("  余额详情: ");
                tokenBalances.forEach((token, balance) ->
                    sb.append(String.format("%s=%.6f ", token, balance)));
            }
            return sb.toString().trim();
        }


        public boolean hasFailures() {
            return !failedTokens.isEmpty();
        }

    }

    /**
     * 处理钱包监控事件
     */
    @EventListener
    public void handleBalanceChangeEvent(BalanceChangeEvent event) {
        insertByFlatChain(event.getChainType(), event.getAddress());
    }

    /**
     * 根据扁平化链类型插入代币余额记录
     * 推荐使用此方法，更符合扁平化配置理念
     */
    @Override
    public void insertByFlatChain(ChainType chainType, String address) {
        log.info("开始处理钱包余额记录: 地址={}, 链类型={}", address, chainType);

        if (chainType == ChainType.TRON) {
            processTronWalletWithFlatConfig(tronConfigFacade, address);
        } else if (chainType == ChainType.BSC) {
            processBscWalletWithFlatConfig(bscConfigFacade, address);
        } else if (chainType == ChainType.SOLANA) {
            processSolanaWalletWithFlatConfig(solanaConfigFacade, address);
        } else if (chainType == ChainType.ARB) {
            processArbWalletWithFlatConfig(arbConfigFacade, address);
        } else if (chainType == ChainType.BASE) {
            processBaseWalletWithFlatConfig(baseConfigFacade, address);
        } else {
            log.warn("不支持的扁平化链类型: {}", chainType);
        }
    }

    /**
     * 统一的余额刷新入口方法
     * 根据区块链名称自动选择对应的处理方法
     */
    @Async
    @Override
    public void refreshBalanceByChain(String chainName, String address) {
        if (StringUtils.isBlank(chainName) || StringUtils.isBlank(address)) {
            log.warn("余额刷新参数无效: chainName={}, address={}", chainName, address);
            return;
        }

        log.info("开始刷新{}链地址{}的余额", chainName, address);

        try {
            switch (chainName.toUpperCase()) {
                case "TRON":
                    processTronWalletWithFlatConfig(tronConfigFacade, address);
                    break;
                case "BSC":
                    processBscWalletWithFlatConfig(bscConfigFacade, address);
                    break;
                case "ARB":
                    processArbWalletWithFlatConfig(arbConfigFacade, address);
                    break;
                case "BASE":
                    processBaseWalletWithFlatConfig(baseConfigFacade, address);
                    break;
                case "SOLANA":
                    processSolanaWalletWithFlatConfig(solanaConfigFacade, address);
                    break;
                default:
                    log.warn("不支持的区块链类型: {}", chainName);
            }
        } catch (Exception e) {
            log.error("刷新{}链地址{}余额失败: {}", chainName, address, e.getMessage(), e);
        }
    }

    /**
     * 查询多链用户钱包代币余额记录
     *
     * @param id 主键
     * @return 多链用户钱包代币余额记录
     */
    @Override
    public WalletCoinRecVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据钱包地址查询代币余额记录
     *
     * @param walletAddress 钱包地址
     * @return 多链用户钱包代币余额记录列表
     */
    @Override
    public List<WalletCoinRecVo> queryByWalletAddress(String walletAddress) {
        LambdaQueryWrapper<WalletCoinRec> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(walletAddress), WalletCoinRec::getWalletAddress, walletAddress);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询每个钱包地址和代币组合的最新记录（专门优化的方法）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 最新记录分页列表
     */
    @Override
    public TableDataInfo<WalletCoinRecVo> queryLatestRecordsPage(WalletCoinRecBo bo, PageQuery pageQuery) {
        Page<WalletCoinRecVo> page = pageQuery.build();

        // 构建查询条件，复用已有的buildQueryWrapper方法
        LambdaQueryWrapper<WalletCoinRec> queryWrapper = buildQueryWrapper(bo);

        // 使用MyBatis-Plus标准方式调用
        IPage<WalletCoinRecVo> result = baseMapper.selectLatestRecordsPage(page, queryWrapper);
        return TableDataInfo.build(result);
    }

    /**
     * 分页查询多链用户钱包代币余额记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 多链用户钱包代币余额记录分页列表
     */
    @Override
    public TableDataInfo<WalletCoinRecVo> queryPageList(WalletCoinRecBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WalletCoinRec> lqw = buildQueryWrapper(bo);

        // 如果需要只查询最新记录，使用专门优化的方法
        if (Boolean.TRUE.equals(bo.getOnlyLatest())) {
            log.info("检测到最新记录查询，建议使用 queryLatestRecordsPage() 方法以获得更好的性能");
            return queryLatestRecordsPage(bo, pageQuery);
        } else {
            // 正常分页查询
            Page<WalletCoinRecVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
            return TableDataInfo.build(result);
        }
    }

    /**
     * 查询符合条件的多链用户钱包代币余额记录列表
     *
     * @param bo 查询条件
     * @return 多链用户钱包代币余额记录列表
     */
    @Override
    public List<WalletCoinRecVo> queryList(WalletCoinRecBo bo) {
        LambdaQueryWrapper<WalletCoinRec> lqw = buildQueryWrapper(bo);

        // 如果需要只查询最新记录，使用专门优化的方法
        if (Boolean.TRUE.equals(bo.getOnlyLatest())) {
            log.info("检测到最新记录查询，建议使用 queryLatestRecordsPage() 方法以获得更好的性能");
            // 对于列表查询中的最新记录需求，创建大分页查询获取所有结果
            PageQuery largePageQuery = new PageQuery();
            largePageQuery.setPageNum(1);
            largePageQuery.setPageSize(10000); // 使用大分页获取结果
            return queryLatestRecordsPage(bo, largePageQuery).getRows();
        } else {
            return baseMapper.selectVoList(lqw);
        }
    }

    /**
     * 构建查询条件Wrapper，更多使用 MyBatis-Plus Lambda 表达式
     */
    private LambdaQueryWrapper<WalletCoinRec> buildQueryWrapper(WalletCoinRecBo bo) {
        Map<String, Object> params = bo.getParams();

        return Wrappers.<WalletCoinRec>lambdaQuery()
            // 基础查询条件
            .eq(bo.getId() != null, WalletCoinRec::getId, bo.getId())
            .like(StringUtils.isNotBlank(bo.getWalletAddress()), WalletCoinRec::getWalletAddress, bo.getWalletAddress())
            .like(StringUtils.isNotBlank(bo.getTokenAddress()), WalletCoinRec::getTokenAddress, bo.getTokenAddress())
            .eq(StringUtils.isNotBlank(bo.getTokenSymbol()), WalletCoinRec::getTokenSymbol, bo.getTokenSymbol())
            .eq(bo.getDecimals() != null, WalletCoinRec::getDecimals, bo.getDecimals())
            .eq(StringUtils.isNotBlank(bo.getChainType()), WalletCoinRec::getChainType, bo.getChainType());

        // 注意：balance > 1 的条件已在SQL中固定设置，不需要在这里添加动态条件
    }

    /**
     * 新增多链用户钱包代币余额记录
     *
     * @param bo 多链用户钱包代币余额记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WalletCoinRecBo bo) {
        WalletCoinRec add = MapstructUtils.convert(bo, WalletCoinRec.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改多链用户钱包代币余额记录
     *
     * @param bo 多链用户钱包代币余额记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WalletCoinRecBo bo) {
        WalletCoinRec update = MapstructUtils.convert(bo, WalletCoinRec.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 校验并批量删除多链用户钱包代币余额记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // 进行业务校验
        return baseMapper.deleteByIds(ids) > 0;
    }

    // ============ 扁平化配置实现方法 ============

    /**
     * 使用扁平化配置处理BSC钱包地址
     */
    @Override
    public void processBscWalletWithFlatConfig(BscConfigFacade bscFacade, String walletAddress) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("钱包地址不能为空");
            return;
        }

        WalletProcessResult result = new WalletProcessResult(walletAddress, "BSC");

        // 处理BNB原生代币
        processBscNativeTokenWithFlatConfig(bscFacade, walletAddress, result);

        // 处理BEP20代币
        processBscTokensWithFlatConfig(bscFacade, walletAddress, result);

        result.finish();
        if (result.hasFailures()) {
            log.warn(result.getSummary());
        } else {
            log.info(result.getSummary());
        }

        // 详细日志（debug级别）
        if (log.isDebugEnabled()) {
            log.debug(result.getDetailedSummary());
        }
    }

    /**
     * 使用扁平化配置处理ARB钱包地址
     */
    @Override
    public void processArbWalletWithFlatConfig(ArbConfigFacade arbFacade, String walletAddress) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("钱包地址不能为空");
            return;
        }

        WalletProcessResult result = new WalletProcessResult(walletAddress, "ARB");

        // 处理ETH原生代币
        processEvmNativeTokenWithFlatConfig(arbFacade, walletAddress, "ARB", "ETH", "_ETH_NATIVE_", result);

        // 处理ERC20代币
        processEvmTokensWithFlatConfig(arbFacade, walletAddress, "ARB", result);

        result.finish();
        if (result.hasFailures()) {
            log.warn(result.getSummary());
        } else {
            log.info(result.getSummary());
        }

        // 详细日志（debug级别）
        if (log.isDebugEnabled()) {
            log.debug(result.getDetailedSummary());
        }
    }

    /**
     * 使用扁平化配置处理BASE钱包地址
     */
    @Override
    public void processBaseWalletWithFlatConfig(BaseConfigFacade baseFacade, String walletAddress) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("钱包地址不能为空");
            return;
        }

        WalletProcessResult result = new WalletProcessResult(walletAddress, "BASE");

        // 处理ETH原生代币
        processEvmNativeTokenWithFlatConfig(baseFacade, walletAddress, "BASE", "ETH", "_ETH_NATIVE_", result);

        // 处理ERC20代币
        processEvmTokensWithFlatConfig(baseFacade, walletAddress, "BASE", result);

        result.finish();
        if (result.hasFailures()) {
            log.warn(result.getSummary());
        } else {
            log.info(result.getSummary());
        }

        // 详细日志（debug级别）
        if (log.isDebugEnabled()) {
            log.debug(result.getDetailedSummary());
        }
    }

    /**
     * 使用扁平化配置处理BNB原生代币
     */
    private void processBscNativeTokenWithFlatConfig(BscConfigFacade bscFacade, String walletAddress, WalletProcessResult result) {
        try {
            // 使用统一入口查询BNB余额
            BigDecimal nativeBalance = evmHelper.balanceGetForRead(walletAddress, "BNB", bscFacade);
            if (nativeBalance != null) {
                WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                balanceRecord.setChainType("BSC");
                balanceRecord.setWalletAddress(walletAddress);
                balanceRecord.setTokenAddress("_BNB_NATIVE_");
                balanceRecord.setTokenSymbol("BNB");
                balanceRecord.setBalance(nativeBalance);
                balanceRecord.setDecimals(18); // BNB默认18位小数
                balanceRecord.setCreateBy(1111L);

                insertByBo(balanceRecord);
                result.addSuccess("BNB", nativeBalance);
            }
        } catch (Exception e) {
            result.addFailure("BNB");
            log.debug("处理BNB原生代币失败: {}, error: {}", walletAddress, e.getMessage());
        }
    }

    /**
     * 使用扁平化配置处理BEP20代币
     */
    private void processBscTokensWithFlatConfig(BscConfigFacade bscFacade, String walletAddress, WalletProcessResult result) {
        // 获取所有启用的代币
        for (String tokenSymbol : bscFacade.getEnabledTokenSymbols()) {
            if ("BNB".equalsIgnoreCase(tokenSymbol)) {
                continue; // 原生代币已经处理过了
            }

            try {
                // 使用统一入口查询代币余额
                BigDecimal balance = evmHelper.balanceGetForRead(walletAddress, tokenSymbol, bscFacade);

                if (balance != null) {
                    WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                    balanceRecord.setChainType("BSC");
                    balanceRecord.setWalletAddress(walletAddress);
                    balanceRecord.setTokenAddress(bscFacade.getContractAddress(tokenSymbol));
                    balanceRecord.setTokenSymbol(tokenSymbol);
                    balanceRecord.setBalance(balance);
                    balanceRecord.setDecimals(bscFacade.getContractDecimals(tokenSymbol));
                    balanceRecord.setCreateBy(1111L);

                    insertByBo(balanceRecord);
                    result.addSuccess(tokenSymbol, balance);
                }
            } catch (Exception e) {
                result.addFailure(tokenSymbol);
                log.debug("处理BSC代币失败: {} {}, error: {}", walletAddress, tokenSymbol, e.getMessage());
            }
        }
    }

    /**
     * 通用EVM原生代币处理方法
     */
    private void processEvmNativeTokenWithFlatConfig(EvmConfigFacade facade, String walletAddress, String chainType, String tokenSymbol, String tokenAddress, WalletProcessResult result) {
        try {
            // 使用统一入口查询原生代币余额
            BigDecimal nativeBalance = evmHelper.balanceGetForRead(walletAddress, tokenSymbol, facade);
            if (nativeBalance != null) {
                WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                balanceRecord.setChainType(chainType);
                balanceRecord.setWalletAddress(walletAddress);
                balanceRecord.setTokenAddress(tokenAddress);
                balanceRecord.setTokenSymbol(tokenSymbol);
                balanceRecord.setBalance(nativeBalance);
                balanceRecord.setDecimals(18); // 原生代币默认18位小数
                balanceRecord.setCreateBy(1111L);

                insertByBo(balanceRecord);
                result.addSuccess(tokenSymbol, nativeBalance);
            }
        } catch (Exception e) {
            result.addFailure(tokenSymbol);
            log.debug("处理{}原生代币失败: {}, error: {}", chainType, walletAddress, e.getMessage());
        }
    }

    /**
     * 通用EVM代币处理方法
     */
    private void processEvmTokensWithFlatConfig(EvmConfigFacade facade, String walletAddress, String chainType, WalletProcessResult result) {
        for (String tokenSymbol : facade.getEnabledTokenSymbols()) {
            try {
                String contractAddress = facade.getContractAddress(tokenSymbol);
                if (contractAddress == null || contractAddress.trim().isEmpty()) {
                    log.debug("{}代币{}缺少合约地址，跳过处理", chainType, tokenSymbol);
                    result.addFailure(tokenSymbol);
                    continue;
                }

                // 使用统一入口查询代币余额
                BigDecimal balance = evmHelper.balanceGetForRead(walletAddress, tokenSymbol, facade);
                if (balance != null) {
                    WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                    balanceRecord.setChainType(chainType);
                    balanceRecord.setWalletAddress(walletAddress);
                    balanceRecord.setTokenAddress(contractAddress);
                    balanceRecord.setTokenSymbol(tokenSymbol);
                    balanceRecord.setBalance(balance);
                    balanceRecord.setDecimals(facade.getContractDecimals(tokenSymbol));
                    balanceRecord.setCreateBy(1111L);

                    insertByBo(balanceRecord);
                    result.addSuccess(tokenSymbol, balance);
                }
            } catch (Exception e) {
                result.addFailure(tokenSymbol);
                log.debug("处理{}代币失败: {} {}, error: {}", chainType, walletAddress, tokenSymbol, e.getMessage());
            }
        }
    }

    /**
     * 使用扁平化配置处理TRON钱包地址
     */
    @Override
    public void processTronWalletWithFlatConfig(TronConfigFacade tronFacade, String walletAddress) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("钱包地址不能为空");
            return;
        }

        WalletProcessResult result = new WalletProcessResult(walletAddress, "TRON");

        // 处理TRX原生代币
        processTronNativeTokenWithFlatConfig(tronFacade, walletAddress, result);

        // 处理TRC20代币
        processTronTokensWithFlatConfig(tronFacade, walletAddress, result);

        result.finish();
        if (result.hasFailures()) {
            log.warn(result.getSummary());
        } else {
            log.info(result.getSummary());
        }

        // 详细日志（debug级别）
        if (log.isDebugEnabled()) {
            log.debug(result.getDetailedSummary());
        }
    }

    /**
     * 使用扁平化配置处理TRX原生代币
     */
    private void processTronNativeTokenWithFlatConfig(TronConfigFacade tronFacade, String walletAddress, WalletProcessResult result) {
        try {
            // 使用统一入口查询TRX余额
            BigDecimal trxBalance = tronHelper.balanceGetForRead(walletAddress, "TRX");

            if (trxBalance != null) {
                WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                balanceRecord.setChainType("TRON");
                balanceRecord.setWalletAddress(walletAddress);
                balanceRecord.setTokenAddress("_TRX_NATIVE_");
                balanceRecord.setTokenSymbol("TRX");
                balanceRecord.setBalance(trxBalance);
                balanceRecord.setDecimals(6); // TRX默认6位小数
                balanceRecord.setCreateBy(1111L);

                insertByBo(balanceRecord);
                result.addSuccess("TRX", trxBalance);
            }
        } catch (Exception e) {
            result.addFailure("TRX");
            log.debug("处理TRX原生代币失败: {}, error: {}", walletAddress, e.getMessage());
        }
    }

    /**
     * 使用扁平化配置处理TRC20代币
     */
    private void processTronTokensWithFlatConfig(TronConfigFacade tronFacade, String walletAddress, WalletProcessResult result) {
        // 获取所有启用的代币
        for (String tokenSymbol : tronFacade.getEnabledTokenSymbols()) {
            if ("TRX".equalsIgnoreCase(tokenSymbol)) {
                continue; // 原生代币已经处理过了
            }

            try {
                // 使用统一入口查询代币余额
                BigDecimal balance = tronHelper.balanceGetForRead(walletAddress, tokenSymbol);

                if (balance != null) {
                    WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                    balanceRecord.setChainType("TRON");
                    balanceRecord.setWalletAddress(walletAddress);
                    balanceRecord.setTokenAddress(tronFacade.getContractAddress(tokenSymbol));
                    balanceRecord.setTokenSymbol(tokenSymbol);
                    balanceRecord.setBalance(balance);
                    balanceRecord.setDecimals(tronFacade.getContractDecimals(tokenSymbol));
                    balanceRecord.setCreateBy(1111L);

                    insertByBo(balanceRecord);
                    result.addSuccess(tokenSymbol, balance);
                }
            } catch (Exception e) {
                result.addFailure(tokenSymbol);
                log.debug("处理TRON代币失败: {} {}, error: {}", walletAddress, tokenSymbol, e.getMessage());
            }
        }
    }

    /**
     * 使用扁平化配置处理Solana钱包地址
     */
    @Override
    public void processSolanaWalletWithFlatConfig(SolanaConfigFacade solanaFacade, String walletAddress) {
        if (walletAddress == null || walletAddress.isEmpty()) {
            log.error("钱包地址不能为空");
            return;
        }

        WalletProcessResult result = new WalletProcessResult(walletAddress, "SOLANA");

        // 处理SOL原生代币
        processSolanaNativeTokenWithFlatConfig(solanaFacade, walletAddress, result);

        // 处理SPL代币
        processSolanaTokensWithFlatConfig(solanaFacade, walletAddress, result);

        result.finish();
        if (result.hasFailures()) {
            log.warn(result.getSummary());
        } else {
            log.info(result.getSummary());
        }

        // 详细日志（debug级别）
        if (log.isDebugEnabled()) {
            log.debug(result.getDetailedSummary());
        }
    }

    /**
     * 使用扁平化配置处理SOL原生代币
     */
    private void processSolanaNativeTokenWithFlatConfig(SolanaConfigFacade solanaFacade, String walletAddress, WalletProcessResult result) {
        try {
            java.math.BigInteger solBalance = solanaHelper.getBalance(walletAddress);

            if (solBalance != null) {
                // 转换为可读格式（SOL有9位小数）
                java.math.BigDecimal readableBalance = new java.math.BigDecimal(solBalance)
                    .divide(java.math.BigDecimal.valueOf(Math.pow(10, 9)), 9, java.math.RoundingMode.HALF_UP);

                WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                balanceRecord.setChainType("SOLANA");
                balanceRecord.setWalletAddress(walletAddress);
                balanceRecord.setTokenAddress("_SOL_NATIVE_");
                balanceRecord.setTokenSymbol("SOL");
                balanceRecord.setBalance(readableBalance);
                balanceRecord.setDecimals(9); // SOL默认9位小数
                balanceRecord.setCreateBy(1111L);

                insertByBo(balanceRecord);
                result.addSuccess("SOL", readableBalance);
            }
        } catch (Exception e) {
            result.addFailure("SOL");
            log.debug("处理SOL原生代币失败: {}, error: {}", walletAddress, e.getMessage());
        }
    }

    /**
     * 使用扁平化配置处理SPL代币
     */
    private void processSolanaTokensWithFlatConfig(SolanaConfigFacade solanaFacade, String walletAddress, WalletProcessResult result) {
        // 获取所有启用的代币
        for (String tokenSymbol : solanaFacade.getEnabledTokenSymbols()) {
            if ("SOL".equalsIgnoreCase(tokenSymbol)) {
                continue; // 原生代币已经处理过了
            }

            try {
                String contractAddress = solanaFacade.getContractAddress(tokenSymbol);
                if (contractAddress == null) {
                    log.debug("代币 {} 的合约地址未配置", tokenSymbol);
                    result.addFailure(tokenSymbol);
                    continue;
                }

                // 首先检查钱包是否有该代币的关联账户
                String tokenAccountAddress = solanaHelper.findTokenAccountSafely(walletAddress, contractAddress);
                if (tokenAccountAddress == null) {
                    // 钱包没有该代币账户，记录零余额
                    recordZeroTokenBalance(walletAddress, contractAddress, tokenSymbol, solanaFacade.getContractDecimals(tokenSymbol), result);
                    continue;
                }

                // 获取SPL代币余额
                var tokenBalance = solanaHelper.getTokenBalance(walletAddress, contractAddress);
                if (tokenBalance != null) {
                    java.math.BigDecimal balance = tokenBalance.getUiAmount() != null ?
                        java.math.BigDecimal.valueOf(tokenBalance.getUiAmount()) : java.math.BigDecimal.ZERO;
                    int decimals = solanaFacade.getContractDecimals(tokenSymbol);

                    WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
                    balanceRecord.setChainType("SOLANA");
                    balanceRecord.setWalletAddress(walletAddress);
                    balanceRecord.setTokenAddress(contractAddress);
                    balanceRecord.setTokenSymbol(tokenSymbol.toUpperCase());
                    balanceRecord.setBalance(balance);
                    balanceRecord.setDecimals(decimals);
                    balanceRecord.setCreateBy(1111L);

                    insertByBo(balanceRecord);
                    result.addSuccess(tokenSymbol, balance);
                }
            } catch (Exception e) {
                result.addFailure(tokenSymbol);
                log.debug("处理SPL代币失败: {} - {}, error: {}", tokenSymbol, walletAddress, e.getMessage());
                // 发生异常时也记录零余额，确保数据完整性
                try {
                    String contractAddress = solanaFacade.getContractAddress(tokenSymbol);
                    if (contractAddress != null) {
                        recordZeroTokenBalance(walletAddress, contractAddress, tokenSymbol, solanaFacade.getContractDecimals(tokenSymbol), result);
                    }
                } catch (Exception ex) {
                    log.debug("记录零余额失败: {} - {}, error: {}", tokenSymbol, walletAddress, ex.getMessage());
                }
            }
        }
    }

    /**
     * 记录零余额代币
     */
    private void recordZeroTokenBalance(String walletAddress, String contractAddress, String tokenSymbol, int decimals, WalletProcessResult result) {
        WalletCoinRecBo balanceRecord = new WalletCoinRecBo();
        balanceRecord.setChainType("SOLANA");
        balanceRecord.setWalletAddress(walletAddress);
        balanceRecord.setTokenAddress(contractAddress);
        balanceRecord.setTokenSymbol(tokenSymbol.toUpperCase());
        balanceRecord.setBalance(java.math.BigDecimal.ZERO);
        balanceRecord.setDecimals(decimals);
        balanceRecord.setCreateBy(1111L);

        insertByBo(balanceRecord);
        result.addSuccess(tokenSymbol, java.math.BigDecimal.ZERO);
    }

}
