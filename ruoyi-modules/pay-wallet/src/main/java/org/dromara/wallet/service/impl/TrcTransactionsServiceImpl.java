package org.dromara.wallet.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.dromara.wallet.domain.TrcTransactions;
import org.dromara.wallet.domain.bo.TrcTransactionsBo;
import org.dromara.wallet.domain.vo.TrcTransactionsVo;
import org.dromara.wallet.mapper.TrcTransactionsMapper;
import org.dromara.wallet.service.ITrcTransactionsService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * TRON链交易记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TrcTransactionsServiceImpl implements ITrcTransactionsService {

    private final TrcTransactionsMapper baseMapper;

    /**
     * 查询TRON链交易记录
     *
     * @param id 主键
     * @return TRON链交易记录
     */
    @Override
    public TrcTransactionsVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据交易ID查询交易记录
     *
     * @param txid 交易ID
     * @return TRON链交易记录
     */
    @Override
    public TrcTransactionsVo queryByTxid(String txid) {
        LambdaQueryWrapper<TrcTransactions> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(txid), TrcTransactions::getTxid, txid);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 根据钱包地址查询交易记录
     *
     * @param address 钱包地址
     * @return TRON链交易记录列表
     */
    @Override
    public List<TrcTransactionsVo> queryByAddress(String address) {
        LambdaQueryWrapper<TrcTransactions> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(address), TrcTransactions::getAddress, address)
           .or()
           .eq(StringUtils.isNotBlank(address), TrcTransactions::getFromaddress, address);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 分页查询TRON链交易记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return TRON链交易记录分页列表
     */
    @Override
    public TableDataInfo<TrcTransactionsVo> queryPageList(TrcTransactionsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TrcTransactions> lqw = buildQueryWrapper(bo);
        Page<TrcTransactionsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的TRON链交易记录列表
     *
     * @param bo 查询条件
     * @return TRON链交易记录列表
     */
    @Override
    public List<TrcTransactionsVo> queryList(TrcTransactionsBo bo) {
        LambdaQueryWrapper<TrcTransactions> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建查询条件Wrapper
     */
    private LambdaQueryWrapper<TrcTransactions> buildQueryWrapper(TrcTransactionsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TrcTransactions> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, TrcTransactions::getId, bo.getId());
        lqw.eq(StringUtils.isNotBlank(bo.getTxid()), TrcTransactions::getTxid, bo.getTxid());
        lqw.eq(bo.getBlockHeight() != null, TrcTransactions::getBlockHeight, bo.getBlockHeight());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), TrcTransactions::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getFromaddress()), TrcTransactions::getFromaddress, bo.getFromaddress());
        lqw.eq(StringUtils.isNotBlank(bo.getContract()), TrcTransactions::getContract, bo.getContract());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), TrcTransactions::getType, bo.getType());
        lqw.eq(bo.getIsSync() != 0, TrcTransactions::getIsSync, bo.getIsSync());
        return lqw;
    }

    /**
     * 新增TRON链交易记录
     *
     * @param bo TRON链交易记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(TrcTransactionsBo bo) {
        TrcTransactions add = MapstructUtils.convert(bo, TrcTransactions.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改TRON链交易记录
     *
     * @param bo TRON链交易记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(TrcTransactionsBo bo) {
        TrcTransactions update = MapstructUtils.convert(bo, TrcTransactions.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 校验并批量删除TRON链交易记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 进行业务校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
