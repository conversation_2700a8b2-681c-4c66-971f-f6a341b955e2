package org.dromara.wallet.wallet.monitor.tron;

import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedissonLockExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.service.WalletScanService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.math.BigInteger;

/**
 * TRON扫描自动启动器
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>应用启动时自动检查配置并启动TRON扫描任务</li>
 *   <li>支持配置开关控制是否自动启动</li>
 *   <li>自动从Redis恢复扫描进度</li>
 *   <li>完整的错误处理和日志记录</li>
 * </ul>
 *
 * <p>配置要求：</p>
 * <ul>
 *   <li>tron.monitor.auto-scan-enabled=true 启用自动扫描</li>
 *   <li>tron.wallet.enabled=true 启用TRON钱包功能</li>
 *   <li>tron.api.enabled=true 启用TRON API</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TronScanApplicationRunner implements ApplicationRunner {

    private final TronConfigFacade tronConfigFacade;
    private final WalletScanService scanService;
    private final LockTemplate lockTemplate;

    /**
     * 分布式锁相关常量
     */
    private static final String SCAN_LOCK_KEY = "tron:scan:auto_start";
    private static final long LOCK_WAIT_TIME = 5000L; // 等待锁的时间：5秒
    private static final long LOCK_LEASE_TIME = 60000L; // 锁的持有时间：60秒

    /**
     * 应用启动时执行自动扫描启动逻辑
     * 使用分布式锁确保集群环境下只有一个节点启动扫描任务
     *
     * @param args 应用启动参数
     */
    @Override
    public void run(ApplicationArguments args) {
        try {
            log.info("TRON扫描自动启动器开始检查配置...");
            EventThreadPool.init(1);

            // 1. 检查基础配置是否启用
            if (!tronConfigFacade.isEnabled()) {
                log.info("TRON链配置未启用，跳过自动扫描启动");
                return;
            }

            // 2. 检查自动扫描配置是否启用
            if (!tronConfigFacade.isAutoScanEnabled()) {
                log.info("TRON自动扫描功能已禁用，跳过自动启动");
                return;
            }

            // 3. 检查扫描服务是否已在运行
            if (scanService.isChainScanning("TRON")) {
                log.warn("TRON扫描任务已在运行中，跳过自动启动");
                return;
            }

            // 4. 尝试获取分布式锁
            log.info("尝试获取TRON扫描启动分布式锁: {}", SCAN_LOCK_KEY);
            LockInfo lockInfo = lockTemplate.lock(SCAN_LOCK_KEY, LOCK_WAIT_TIME, LOCK_LEASE_TIME, RedissonLockExecutor.class);

            if (lockInfo == null) {
                log.info("未能获取TRON扫描启动锁，可能其他节点已启动扫描任务");
                return;
            }

            try {
                log.info("成功获取TRON扫描启动锁，开始启动扫描任务");

                // 5. 再次检查扫描服务状态（双重检查）
                if (scanService.isChainScanning("TRON")) {
                    log.warn("获取锁后发现TRON扫描任务已在运行中，跳过启动");
                    return;
                }

                // 6. 获取自动扫描配置参数
                long startBlockLong = tronConfigFacade.getAutoScanStartBlock();
                long scanPeriod = tronConfigFacade.getAutoScanPeriod();

                // 7. 转换起始区块号
                BigInteger startBlock = BigInteger.valueOf(Math.max(0, startBlockLong));

                log.info("开始自动启动TRON扫描任务，起始区块: {}, 扫描周期: {}ms", startBlock, scanPeriod);

                // 8. 启动扫描任务
                boolean success = scanService.startTronChainScan(startBlock, scanPeriod);

                if (success) {
                    log.info("TRON扫描任务自动启动成功，当前节点成为主扫描节点");
                } else {
                    log.error("TRON扫描任务自动启动失败");
                }

            } finally {
                // 9. 释放分布式锁
                lockTemplate.releaseLock(lockInfo);
                log.info("已释放TRON扫描启动分布式锁");
            }

        } catch (Exception e) {
            log.error("TRON扫描自动启动过程中发生异常: {}", e.getMessage(), e);
        }
    }
}
