package org.dromara.wallet.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.config.facade.BscConfigFacade;
import org.dromara.wallet.config.facade.ArbConfigFacade;
import org.dromara.wallet.config.facade.BaseConfigFacade;
import org.dromara.wallet.config.facade.SolanaConfigFacade;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.domain.bo.WalletCoinRecBo;
import org.dromara.wallet.domain.vo.WalletCoinRecVo;

import java.util.Collection;
import java.util.List;

/**
 * 多链用户钱包代币余额记录Service接口
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public interface IWalletCoinRecService {


    // 扁平化配置方法（具体实现）
    void processBscWalletWithFlatConfig(BscConfigFacade bscFacade, String walletAddress);

    void processArbWalletWithFlatConfig(ArbConfigFacade arbFacade, String walletAddress);

    void processBaseWalletWithFlatConfig(BaseConfigFacade baseFacade, String walletAddress);

    void insertByFlatChain(ChainType chainType, String address);

    /**
     * 统一的余额刷新入口方法
     * 根据区块链名称自动选择对应的处理方法
     *
     * @param chainName 区块链名称（如：BSC、TRON、ARB、BASE、SOLANA）
     * @param address   钱包地址
     */
    void refreshBalanceByChain(String chainName, String address);

    /**
     * 查询多链用户钱包代币余额记录
     *
     * @param id 主键
     * @return 多链用户钱包代币余额记录
     */
    WalletCoinRecVo queryById(Long id);

    /**
     * 根据钱包地址查询代币余额记录
     *
     * @param walletAddress 钱包地址
     * @return 多链用户钱包代币余额记录列表
     */
    List<WalletCoinRecVo> queryByWalletAddress(String walletAddress);

    /**
     * 查询每个钱包地址和代币组合的最新记录（专门优化的方法）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 最新记录分页列表
     */
    TableDataInfo<WalletCoinRecVo> queryLatestRecordsPage(WalletCoinRecBo bo, PageQuery pageQuery);

    /**
     * 分页查询多链用户钱包代币余额记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 多链用户钱包代币余额记录分页列表
     */
    TableDataInfo<WalletCoinRecVo> queryPageList(WalletCoinRecBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的多链用户钱包代币余额记录列表
     *
     * @param bo 查询条件
     * @return 多链用户钱包代币余额记录列表
     */
    List<WalletCoinRecVo> queryList(WalletCoinRecBo bo);

    /**
     * 新增多链用户钱包代币余额记录
     *
     * @param bo 多链用户钱包代币余额记录
     * @return 是否新增成功
     */
    Boolean insertByBo(WalletCoinRecBo bo);

    /**
     * 修改多链用户钱包代币余额记录
     *
     * @param bo 多链用户钱包代币余额记录
     * @return 是否修改成功
     */
    Boolean updateByBo(WalletCoinRecBo bo);

    /**
     * 校验并批量删除多链用户钱包代币余额记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void processTronWalletWithFlatConfig(TronConfigFacade tronFacade, String walletAddress);

    void processSolanaWalletWithFlatConfig(SolanaConfigFacade solanaFacade, String walletAddress);
}
