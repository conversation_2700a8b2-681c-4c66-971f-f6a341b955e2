package org.dromara.wallet.wallet.helper;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.tron.TronApiConfig;
import org.dromara.wallet.wallet.exception.TronBlockException;
import org.dromara.wallet.wallet.utils.TronAddressUtils;
import org.dromara.wallet.wallet.utils.TronTransactionSigner;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * TRON HTTP API服务
 * 提供基于HTTP API的TRON区块链操作，替代TRON Java SDK
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
public class TronHttpApiHelper {

    @Qualifier("tronRestTemplate")
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private TronApiConfig apiConfig;

    @Resource
    private ObjectMapper objectMapper;

    // ==================== 账户相关API ====================

    /**
     * 获取账户信息
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public JsonNode getAccount(String address) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getaccount";

        // 确保地址格式正确（Base58格式）
        String normalizedAddress = ensureBase58Address(address);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("address", normalizedAddress);
        requestBody.put("visible", true);

        return executePost(url, requestBody);
    }

    /**
     * 获取账户余额（TRX）
     */
    public BigInteger getAccountBalance(String address) {
        JsonNode account = getAccount(address);
        if (account != null && account.has("balance")) {
            return new BigInteger(account.get("balance").asText("0"));
        }
        return BigInteger.ZERO;
    }

    /**
     * 获取账户资源信息
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public JsonNode getAccountResource(String address) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getaccountresource";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("address", address);
        requestBody.put("visible", true);

        return executePost(url, requestBody);
    }

    // ==================== 交易相关API ====================

    /**
     * 创建TRX转账交易
     */
    public JsonNode createTrxTransaction(String fromAddress, String toAddress, long amount) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/createtransaction";

        // 确保地址格式正确（Base58格式）
        String normalizedFromAddress = ensureBase58Address(fromAddress);
        String normalizedToAddress = ensureBase58Address(toAddress);

        log.debug("TRON API: 创建TRX转账交易 | from: {} | to: {} | amount: {} Sun",
            normalizedFromAddress, normalizedToAddress, amount);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("owner_address", normalizedFromAddress);
        requestBody.put("to_address", normalizedToAddress);
        requestBody.put("amount", amount);
        requestBody.put("visible", true);

        JsonNode result = executePost(url, requestBody);

        // 验证交易创建结果
        validateTransactionCreationResult(result, "TRX转账");

        return result;
    }

    /**
     * 创建TRC20代币转账交易
     */
    public JsonNode createTrc20Transaction(String fromAddress, String toAddress,
                                           String contractAddress, long amount) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/triggersmartcontract";

        // 确保地址格式正确（Base58格式）
        String normalizedFromAddress = ensureBase58Address(fromAddress);
        String normalizedContractAddress = ensureBase58Address(contractAddress);
        String normalizedToAddress = ensureBase58Address(toAddress);

        // 构造TRC20 transfer方法调用 - 注意：parameter字段不需要包含函数选择器
        String toAddressHex = TronAddressUtils.addressToHex(normalizedToAddress);
        String amountHex = String.format("%064x", amount);
        String parameter = toAddressHex + amountHex; // 只传参数，API会自动添加函数选择器

        log.debug("TRON API: 创建TRC20转账交易 | from: {} | to: {} | contract: {} | amount: {}",
            normalizedFromAddress, normalizedToAddress, normalizedContractAddress, amount);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("owner_address", normalizedFromAddress);
        requestBody.put("contract_address", normalizedContractAddress);
        requestBody.put("function_selector", "transfer(address,uint256)");
        requestBody.put("parameter", parameter);
        requestBody.put("fee_limit", 50000000); // 50 TRX
        requestBody.put("call_value", 0);
        requestBody.put("visible", true);

        JsonNode result = executePost(url, requestBody);

        // 记录API响应的关键信息
        logTrc20CreationResponse(result, normalizedFromAddress, normalizedToAddress, normalizedContractAddress, amount);

        // 验证交易创建结果
        validateTransactionCreationResult(result, "TRC20转账");

        return result;
    }

    /**
     * 签名交易
     * 使用私钥对交易进行签名
     */
    public JsonNode signTransaction(JsonNode transaction, String privateKey) {
        try {
            log.debug("TRON API: 开始签名交易");

            // 验证交易数据
            validateTransactionForSigning(transaction);

            // 使用 TronTransactionSigner 进行交易签名
            JsonNode signedTransaction = TronTransactionSigner.signTransaction(transaction, privateKey);

            // 验证签名结果
            validateSignedTransaction(signedTransaction);

            log.debug("TRON API: 交易签名完成");
            return signedTransaction;

        } catch (Exception e) {
            log.error("TRON API: 交易签名失败 | error: {}", e.getMessage());
            throw new TronBlockException("交易签名失败: " + e.getMessage());
        }
    }

    /**
     * 验证交易是否适合签名
     */
    private void validateTransactionForSigning(JsonNode transaction) {
        if (transaction == null) {
            throw new TronBlockException("交易数据为空，无法签名");
        }

        // 检查是否已经有签名
        if (transaction.has("signature") && transaction.get("signature").isArray() &&
            transaction.get("signature").size() > 0) {
            log.warn("TRON API: 交易已包含签名，将被覆盖");
        }

        // 检查交易结构
        JsonNode actualTransaction = transaction.has("transaction") ? transaction.get("transaction") : transaction;
        if (!actualTransaction.has("raw_data")) {
            throw new TronBlockException("交易缺少raw_data字段，无法签名");
        }
    }

    /**
     * 验证签名后的交易
     */
    private void validateSignedTransaction(JsonNode signedTransaction) {
        if (signedTransaction == null) {
            throw new TronBlockException("签名后交易为空");
        }

        // 检查是否有签名字段
        if (!signedTransaction.has("signature")) {
            throw new TronBlockException("签名失败：交易缺少signature字段");
        }

        JsonNode signatures = signedTransaction.get("signature");
        if (!signatures.isArray() || signatures.size() == 0) {
            throw new TronBlockException("签名失败：signature字段为空或格式错误");
        }

        // 检查签名长度（TRON签名通常是130个字符的十六进制字符串）
        String signature = signatures.get(0).asText();
        if (signature.length() != 130) {
            log.warn("TRON API: 签名长度异常 | length: {} | signature: {}", signature.length(), signature);
        }

        log.debug("TRON API: 签名验证通过 | signature: {}...", signature.substring(0, 20));
    }

    /**
     * 广播交易
     */
    public JsonNode broadcastTransaction(JsonNode signedTransaction) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/broadcasttransaction";
        return executePost(url, signedTransaction);
    }

    /**
     * 签名并广播交易的便捷方法
     */
    public String signAndBroadcastTransaction(JsonNode transaction, String privateKey) {
        // 1. 签名交易
        JsonNode signedTransaction = signTransaction(transaction, privateKey);

        // 2. 广播交易
        JsonNode result = broadcastTransaction(signedTransaction);

        // 3. 验证广播结果
        validateBroadcastResult(result);

        // 4. 提取交易哈希
        if (result.has("txid")) {
            return result.get("txid").asText();
        } else if (result.has("transaction")) {
            JsonNode txInfo = result.get("transaction");
            if (txInfo.has("txID")) {
                return txInfo.get("txID").asText();
            }
        }

        throw new TronBlockException("无法获取交易哈希");
    }

    /**
     * 验证广播交易的结果
     * 检查TRON API返回的结果是否表示广播成功
     * <p>
     * TRON广播API返回格式：
     * 成功: {"result": true, "txid": "..."}
     * 失败: {"result": false, "code": "ERROR_CODE", "message": "error details", "txid": "..."}
     * 错误: {"Error": "error message"}
     */
    private void validateBroadcastResult(JsonNode result) {
        if (result == null) {
            throw new TronBlockException("广播交易返回空结果");
        }

        // 移除详细响应日志，减少冗余输出

        // 1. 检查是否有Error字段（API级别错误）
        if (result.has("Error")) {
            String error = result.get("Error").asText();
            log.error("TRON API: 交易广播API错误 | error: {}", error);
            throw new TronBlockException("交易广播API错误: " + error);
        }

        // 2. 检查result字段（广播结果）
        if (result.has("result")) {
            boolean success = result.get("result").asBoolean();

            if (!success) {
                // 广播失败，提取详细错误信息
                String errorCode = result.has("code") ? result.get("code").asText() : "UNKNOWN";
                String errorMessage = result.has("message") ? result.get("message").asText() : "未知错误";
                String txid = result.has("txid") ? result.get("txid").asText() : "unknown";

                log.warn("交易广播失败: {} | {} | txid: {}", errorCode, errorMessage, txid);

                // 根据错误代码分类处理
                throw createBroadcastException(errorCode, errorMessage, txid);
            } else {
                // 广播成功
                String txid = result.has("txid") ? result.get("txid").asText() : "unknown";
                log.debug("交易广播成功: {}", txid);
                return;
            }
        }

        // 3. 优先检查是否有错误标识（code和message字段）
        if (result.has("code") || result.has("message")) {
            String errorCode = result.has("code") ? result.get("code").asText() : "UNKNOWN";
            String errorMessage = result.has("message") ? result.get("message").asText() : "未知错误";
            String txid = result.has("txid") ? result.get("txid").asText() : "unknown";

            log.warn("交易广播失败: {} | {} | txid: {}", errorCode, errorMessage, txid);

            // 根据错误代码分类处理
            throw createBroadcastException(errorCode, errorMessage, txid);
        }

        // 4. 检查是否有txid字段但没有result字段（某些情况下的成功响应）
        if (result.has("txid")) {
            String txid = result.get("txid").asText();
            log.debug("交易广播成功（无result字段）: {}", txid);
            return;
        }

        // 5. 响应格式异常
        log.warn("TRON API: 广播响应格式异常 | response: {}", result.toString());
        throw new TronBlockException("广播响应格式异常，无法确定广播结果");
    }

    /**
     * 根据错误代码创建相应的异常
     */
    private TronBlockException createBroadcastException(String errorCode, String errorMessage, String txid) {
        String baseMessage = String.format("交易广播失败[%s]: %s", errorCode, errorMessage);

        // 根据常见错误代码进行分类
        switch (errorCode.toUpperCase()) {
            case "SIGERROR":
                return new TronBlockException(baseMessage + " (签名错误)");
            case "CONTRACT_VALIDATE_ERROR":
                return new TronBlockException(baseMessage + " (合约验证错误)");
            case "TAPOS_ERROR":
                return new TronBlockException(baseMessage + " (TAPOS检查错误)");
            case "DUP_TRANSACTION_ERROR":
                return new TronBlockException(baseMessage + " (重复交易)");
            case "TRANSACTION_EXPIRATION_ERROR":
                return new TronBlockException(baseMessage + " (交易过期)");
            case "SERVER_BUSY":
                return new TronBlockException(baseMessage + " (服务器繁忙)");
            case "TOO_BIG_TRANSACTION_ERROR":
                return new TronBlockException(baseMessage + " (交易过大)");
            case "ACCOUNT_RESOURCE_INSUFFICIENT":
                return new TronBlockException(baseMessage + " (账户资源不足)");
            case "VALIDATE_SIGNATURE_ERROR":
                return new TronBlockException(baseMessage + " (签名验证失败)");
            default:
                return new TronBlockException(baseMessage);
        }
    }

    /**
     * 验证交易创建结果
     * 在交易创建阶段就检测潜在问题
     */
    private void validateTransactionCreationResult(JsonNode result, String transactionType) {
        if (result == null) {
            throw new TronBlockException(transactionType + "创建失败: API返回空结果");
        }

        // 只在失败时记录详细响应信息，减少冗余日志

        // 1. 检查是否有Error字段
        if (result.has("Error")) {
            String error = result.get("Error").asText();
            log.error("TRON API: {}创建失败 | error: {}", transactionType, error);
            log.debug("TRON API: {}创建失败详细响应 | response: {}", transactionType, result.toString());
            throw new TronBlockException(transactionType + "创建失败: " + error);
        }

        // 2. 检查是否有result字段且为false
        if (result.has("result")) {
            boolean success = result.get("result").asBoolean();
            if (!success) {
                // 增强错误信息提取逻辑
                String errorMessage = extractDetailedErrorMessage(result, transactionType);

                // 检查是否为余额不足错误，优化日志级别
                if (isInsufficientBalanceError(errorMessage) ||
                    (result.has("message") && isInsufficientBalanceError(result.get("message").asText())) ||
                    (result.has("code") && isInsufficientBalanceErrorCode(result.get("code").asText()))) {

                    log.info("TRON API: {}创建失败（余额不足） | 详细错误: {}", transactionType, errorMessage);
                    log.debug("TRON API: {}创建失败详细响应 | response: {}", transactionType, result.toString());
                } else {
                    log.error("TRON API: {}创建失败 | 详细错误: {}", transactionType, errorMessage);
                    log.debug("TRON API: {}创建失败详细响应 | response: {}", transactionType, result.toString());
                }

                throw new TronBlockException(transactionType + "创建失败: " + errorMessage);
            }
        }

        // 3. 检查是否有必要的交易字段
        boolean hasTransaction = result.has("transaction") || result.has("raw_data") || result.has("txID");
        if (!hasTransaction) {
            log.error("TRON API: {}创建失败 | 缺少交易数据", transactionType);
            log.debug("TRON API: {}创建失败详细响应 | response: {}", transactionType, result.toString());
            throw new TronBlockException(transactionType + "创建失败: 响应中缺少交易数据");
        }

        // 4. 检查交易是否有效（有raw_data或transaction字段）
        JsonNode transaction = result.has("transaction") ? result.get("transaction") : result;
        if (transaction.has("raw_data")) {
            JsonNode rawData = transaction.get("raw_data");

            // 检查是否有合约数据
            if (!rawData.has("contract") || !rawData.get("contract").isArray() ||
                rawData.get("contract").isEmpty()) {
                log.error("TRON API: {}创建失败 | 交易缺少合约数据", transactionType);
                throw new TronBlockException(transactionType + "创建失败: 交易缺少合约数据");
            }

            // 检查过期时间
            if (rawData.has("expiration")) {
                long expiration = rawData.get("expiration").asLong();
                long currentTime = System.currentTimeMillis();
                if (expiration <= currentTime) {
                    log.error("TRON API: {}创建失败 | 交易已过期 | expiration: {} | current: {}",
                        transactionType, expiration, currentTime);
                    throw new TronBlockException(transactionType + "创建失败: 交易已过期");
                }
            }
        }

        log.debug("TRON API: {}创建成功", transactionType);
    }

    /**
     * 提取详细的错误信息
     * 从API响应中尽可能多地提取错误信息，帮助诊断问题
     * 增强版：支持识别余额不足等常见错误
     */
    private String extractDetailedErrorMessage(JsonNode result, String transactionType) {
        StringBuilder errorBuilder = new StringBuilder();

        // 1. 检查message字段
        if (result.has("message") && !result.get("message").asText().trim().isEmpty()) {
            String message = result.get("message").asText();
            errorBuilder.append(message);

            // 检查是否为余额不足相关错误
            if (isInsufficientBalanceError(message)) {
                return "余额不足：" + message;
            }
        }

        // 2. 检查code字段
        if (result.has("code")) {
            String code = result.get("code").asText();
            if (!code.trim().isEmpty()) {
                if (!errorBuilder.isEmpty()) {
                    errorBuilder.append(" | ");
                }
                errorBuilder.append("错误码: ").append(code);

                // 检查是否为余额不足相关错误码
                if (isInsufficientBalanceErrorCode(code)) {
                    return "余额不足：错误码 " + code;
                }
            }
        }

        // 3. 检查transaction字段中的错误信息
        if (result.has("transaction")) {
            JsonNode transaction = result.get("transaction");
            if (transaction.has("ret") && transaction.get("ret").isArray()) {
                JsonNode retArray = transaction.get("ret");
                if (!retArray.isEmpty()) {
                    JsonNode ret = retArray.get(0);
                    if (ret.has("contractRet")) {
                        String contractRet = ret.get("contractRet").asText();
                        if (!"SUCCESS".equals(contractRet)) {
                            if (!errorBuilder.isEmpty()) {
                                errorBuilder.append(" | ");
                            }
                            errorBuilder.append("合约执行结果: ").append(contractRet);
                        }
                    }
                }
            }
        }

        // 4. 检查常见的TRON错误字段
        String[] errorFields = {"resMessage", "result_message", "error_message", "errorMessage"};
        for (String field : errorFields) {
            if (result.has(field) && !result.get(field).asText().trim().isEmpty()) {
                if (!errorBuilder.isEmpty()) {
                    errorBuilder.append(" | ");
                }
                errorBuilder.append(field).append(": ").append(result.get(field).asText());
            }
        }

        // 5. 如果仍然没有找到错误信息，提供更有用的默认信息
        if (errorBuilder.isEmpty()) {
            errorBuilder.append("API返回result=false但未提供具体错误信息");

            // 添加响应结构信息以帮助调试
            if (result.has("transaction")) {
                errorBuilder.append("，响应包含transaction数据");
            }

            // 记录完整响应以便调试
            log.warn("TRON API: {}创建失败但无详细错误信息 | 完整响应: {}", transactionType, result);
        }

        return errorBuilder.toString();
    }

    /**
     * 判断是否为余额不足错误信息
     * 检查TRON常见的余额不足错误描述
     */
    private boolean isInsufficientBalanceError(String message) {
        if (message == null) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("insufficient") ||
               lowerMessage.contains("balance") ||
               lowerMessage.contains("not enough") ||
               lowerMessage.contains("余额不足") ||
               lowerMessage.contains("insufficient funds") ||
               lowerMessage.contains("insufficient balance") ||
               lowerMessage.contains("transfer amount exceeds balance") ||
               lowerMessage.contains("account does not exist") ||
               lowerMessage.contains("validate transfer error");
    }

    /**
     * 判断是否为余额不足相关的错误码
     * 检查TRON常见的余额不足错误码
     */
    private boolean isInsufficientBalanceErrorCode(String code) {
        if (code == null) {
            return false;
        }

        // TRON常见的余额不足相关错误码
        return "INSUFFICIENT_BALANCE".equals(code) ||
               "ACCOUNT_NOT_EXISTS".equals(code) ||
               "VALIDATE_TRANSFER_ERROR".equals(code) ||
               "CONTRACT_VALIDATE_ERROR".equals(code);
    }

    /**
     * 记录TRC20创建响应的关键信息
     * 简化日志记录，避免与validateTransactionCreationResult重复
     */
    private void logTrc20CreationResponse(JsonNode result, String fromAddress, String toAddress,
                                          String contractAddress, long amount) {
        if (result == null) {
            log.warn("TRON API: TRC20交易创建响应为空");
            return;
        }

        // 只记录特殊情况，避免与通用验证方法重复
        boolean hasResult = result.has("result");
        boolean hasTransaction = result.has("transaction");

        // 只在有transaction但result=false的异常情况下记录
        if (hasResult && hasTransaction) {
            boolean success = result.get("result").asBoolean();
            if (!success) {
                // 检查是否为余额不足错误，优化日志描述
                boolean isBalanceError = false;
                if (result.has("message")) {
                    isBalanceError = isInsufficientBalanceError(result.get("message").asText());
                }
                if (!isBalanceError && result.has("code")) {
                    isBalanceError = isInsufficientBalanceErrorCode(result.get("code").asText());
                }

                if (isBalanceError) {
                    log.debug("TRC20余额不足 - API返回transaction数据但result=false | {} -> {} | contract: {}",
                        fromAddress, toAddress, contractAddress);
                } else {
                    log.debug("TRC20异常情况 - API返回transaction数据但result=false | {} -> {} | contract: {}",
                        fromAddress, toAddress, contractAddress);
                }
            }
        }
    }

    /**
     * 查询交易信息
     */
    public JsonNode getTransactionById(String txId) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/gettransactionbyid";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("value", txId);

        return executePost(url, requestBody);
    }

    /**
     * 查询交易信息（包含详细信息）
     * 包含事件日志、资源消耗等详细信息
     */
    public JsonNode getTransactionInfoById(String txId) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/gettransactioninfobyid";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("value", txId);

        JsonNode result = executePost(url, requestBody);

        // 简化事件日志记录，只在有多个事件时记录
        if (result != null && result.has("log")) {
            JsonNode logArray = result.get("log");
            if (logArray.isArray() && logArray.size() > 1) {
                log.debug("TRON API: 交易包含{}个事件日志 | txId: {}", logArray.size(), txId);
            }
        }

        return result;
    }

    // ==================== 地址格式处理工具方法 ====================

    /**
     * 确保地址为Base58格式
     * 如果是Hex格式则转换为Base58格式，如果已经是Base58格式则直接返回
     *
     * @param address 输入地址（可能是Base58或Hex格式）
     * @return Base58格式的地址
     */
    private String ensureBase58Address(String address) {
        if (address == null || address.trim().isEmpty()) {
            throw new IllegalArgumentException("地址不能为空");
        }

        String trimmedAddress = address.trim();

        // 如果已经是Base58格式（以T开头，长度34），直接返回
        if (trimmedAddress.startsWith("T") && trimmedAddress.length() == 34) {
            return trimmedAddress;
        }

        // 如果是Hex格式，转换为Base58格式
        if (isHexAddress(trimmedAddress)) {
            try {
                String base58Address = TronAddressUtils.hexToAddress(trimmedAddress);
                log.debug("地址从Hex转换为Base58: {} -> {}", trimmedAddress, base58Address);
                return base58Address;
            } catch (Exception e) {
                log.error("地址格式转换失败: address={}, error={}", trimmedAddress, e.getMessage());
                throw new IllegalArgumentException("无效的地址格式: " + trimmedAddress);
            }
        }

        // 不支持的格式
        throw new IllegalArgumentException("不支持的地址格式: " + trimmedAddress + " (长度: " + trimmedAddress.length() + ")");
    }

    /**
     * 判断是否为Hex格式的地址
     */
    private boolean isHexAddress(String address) {
        if (address == null) {
            return false;
        }

        String normalized = address.toLowerCase();

        // 移除0x前缀
        if (normalized.startsWith("0x")) {
            normalized = normalized.substring(2);
        }

        // 检查长度和字符
        return normalized.length() == 42 && normalized.matches("^[0-9a-f]+$") && normalized.startsWith("41");
    }

    // ==================== TRC20代币相关API ====================

    /**
     * 查询TRC20代币余额
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public BigInteger getTrc20Balance(String address, String contractAddress) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/triggerconstantcontract";

        try {
            // 验证地址格式
            if (!TronAddressUtils.isValidTronAddress(address)) {
                throw new IllegalArgumentException("无效的TRON地址: " + address);
            }
            if (!TronAddressUtils.isValidTronAddress(contractAddress)) {
                throw new IllegalArgumentException("无效的合约地址: " + contractAddress);
            }

            // 构造balanceOf方法调用 - 注意：parameter字段不需要包含函数选择器
            String parameter = TronAddressUtils.addressToHex(address); // 只传地址参数，API会自动添加函数选择器

            log.debug("TRC20余额查询参数: address={}, contract={}, parameter={}",
                address, contractAddress, parameter);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("owner_address", address);
            requestBody.put("contract_address", contractAddress);
            requestBody.put("function_selector", "balanceOf(address)");
            requestBody.put("parameter", parameter);
            requestBody.put("visible", true);

            JsonNode result = executePost(url, requestBody);

            // 简化日志记录，只在异常时记录详细响应

            if (result.has("constant_result")) {
                JsonNode constantResult = result.get("constant_result");
                if (constantResult.isArray() && !constantResult.isEmpty()) {
                    String hexResult = constantResult.get(0).asText();
                    if (hexResult != null && !hexResult.isEmpty()) {
                        log.debug("TRC20余额十六进制结果: {}", hexResult);
                        return new BigInteger(hexResult, 16);
                    }
                }
            }

            // 检查是否有错误信息
            if (result.has("Error")) {
                String error = result.get("Error").asText();
                log.error("TRC20余额查询API错误: {}", error);
                log.debug("TRC20余额查询详细响应: {}", result.toString());
                throw new TronBlockException("TRC20余额查询失败: " + error);
            }

            log.warn("TRC20余额查询返回空结果: address={}, contract={}", address, contractAddress);
            return BigInteger.ZERO;

        } catch (Exception e) {
            log.error("TRC20余额查询异常: address={}, contract={}, error={}",
                address, contractAddress, e.getMessage());
            throw new TronBlockException("TRC20余额查询失败: " + e.getMessage());
        }
    }

    // ==================== 区块查询API ====================

    /**
     * 获取最新区块
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public JsonNode getLatestBlock() {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getnowblock";
        return executePost(url, new HashMap<>());
    }

    /**
     * 根据区块高度获取区块
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public JsonNode getBlockByNumber(BigInteger blockNumber) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getblockbynum";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("num", blockNumber.longValue());

        return executePost(url, requestBody);
    }

    /**
     * 批量获取区块（GetBlockByLimitNext API）
     * 用于高效的区块扫描，一次获取多个连续区块
     *
     * @param startNum 起始区块号（包含）
     * @param endNum   结束区块号（不包含）
     * @return 区块列表
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public JsonNode getBlockByLimitNext(BigInteger startNum, BigInteger endNum) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getblockbylimitnext";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("startNum", startNum.longValue());
        requestBody.put("endNum", endNum.longValue());

        JsonNode result = executePost(url, requestBody);

        if (log.isDebugEnabled()) {
            if (result != null && result.has("block")) {
                JsonNode blocks = result.get("block");
                if (blocks.isArray()) {
                    log.debug("批量获取区块成功: 起始区块={}, 结束区块={}, 实际获取数量={}",
                        startNum, endNum, blocks.size());
                }
            }
        }

        return result;
    }

    // ==================== 价格和资源API ====================

    /**
     * 获取能量价格
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public BigInteger getEnergyPrice() {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getenergyprices";

        JsonNode result = executeGet(url);
        if (result.has("prices")) {
            String prices = result.get("prices").asText();
            String[] priceEntries = prices.split(",");
            String lastEntry = priceEntries[priceEntries.length - 1];
            String[] parts = lastEntry.split(":");
            return BigInteger.valueOf(Long.parseLong(parts[1]));
        }

        throw new TronBlockException("获取能量价格失败");
    }

    /**
     * 查询 质押trx获取能量的数量（比例）
     */
    @Retryable(value = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2))
    public BigDecimal getTrxEnergyRatio(String address) {
        String url = apiConfig.getScanEndpoint() + "/api/accountv2?address=" + address;
        String result = HttpUtil.get(url);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (!jsonObject.isEmpty()) {
            BigDecimal energyCost = new BigDecimal(jsonObject.get("energyCost").toString());
            //保留4位
            return energyCost.setScale(6, RoundingMode.UP);
        }
        log.error("获取质押 1 TRX 可获得多少能量失败");
        throw new TronBlockException("获取能量价格失败");
    }


    // ==================== 能量代理API ====================

    /**
     * 代理资源给指定地址
     *
     * @param ownerAddress 资源提供方地址
     * @param receiverAddress 资源接收方地址
     * @param balance 代理的TRX数量（Sun单位）
     * @param resource 资源类型：0=带宽，1=能量
     * @param lock 是否锁定（通常为true）
     * @return 创建的交易对象
     */
    public JsonNode createDelegateResourceTransaction(String ownerAddress, String receiverAddress,
                                                     long balance, int resource, boolean lock) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/delegateresource";

        // 确保地址格式正确
        String normalizedOwnerAddress = ensureBase58Address(ownerAddress);
        String normalizedReceiverAddress = ensureBase58Address(receiverAddress);

        log.debug("TRON API: 创建资源代理交易 | receiver: {} | balance: {} Sun | resource: {}",
            normalizedReceiverAddress, balance, resource);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("owner_address", normalizedOwnerAddress);
        requestBody.put("receiver_address", normalizedReceiverAddress);
        requestBody.put("balance", balance);
        requestBody.put("resource", resource);
        requestBody.put("lock", lock);
        requestBody.put("visible", true);

        JsonNode result = executePost(url, requestBody);

        // 验证交易创建结果
        validateTransactionCreationResult(result, "资源代理");

        return result;
    }

    /**
     * 查询代理的资源信息
     *
     * @param fromAddress 资源提供方地址
     * @param toAddress 资源接收方地址
     * @return 代理资源信息
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public JsonNode getDelegatedResource(String fromAddress, String toAddress) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getdelegatedresource";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("fromAddress", fromAddress);
        requestBody.put("toAddress", toAddress);
        requestBody.put("visible", true);

        return executePost(url, requestBody);
    }

    /**
     * 查询账户的代理资源信息（V2版本）
     *
     * @param address 账户地址
     * @return 代理资源信息
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public JsonNode getDelegatedResourceV2(String address) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getdelegatedresourcev2";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("value", address);
        requestBody.put("visible", true);

        return executePost(url, requestBody);
    }

    /**
     * 代理资源给指定地址（V2版本）
     * 使用已质押的资源分配给目标地址，提高资金利用效率
     *
     * @param ownerAddress 资源提供方地址
     * @param receiverAddress 资源接收方地址
     * @param balance 代理的TRX数量（Sun单位）
     * @param resource 资源类型：0=带宽，1=能量
     * @param lock 是否锁定（通常为true）
     * @return 创建的交易对象
     */
    public JsonNode createDelegateResourceV2Transaction(String ownerAddress, String receiverAddress,
                                                       long balance, int resource, boolean lock) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/delegateresourcev2";

        // 确保地址格式正确
        String normalizedOwnerAddress = ensureBase58Address(ownerAddress);
        String normalizedReceiverAddress = ensureBase58Address(receiverAddress);

        log.debug("TRON API: 创建资源代理交易V2 | owner: {} | receiver: {} | balance: {} Sun | resource: {} | lock: {}",
            normalizedOwnerAddress, normalizedReceiverAddress, balance, resource, lock);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("owner_address", normalizedOwnerAddress);
        requestBody.put("receiver_address", normalizedReceiverAddress);
        requestBody.put("balance", balance);
        requestBody.put("resource", resource);
        requestBody.put("lock", lock);
        requestBody.put("visible", true);

        JsonNode result = executePost(url, requestBody);

        // 验证交易创建结果
        validateTransactionCreationResult(result, "资源代理V2");

        return result;
    }

    /**
     * 回收代理的资源
     * 将之前代理给目标地址的资源回收回来
     *
     * @param ownerAddress 资源提供方地址
     * @param receiverAddress 资源接收方地址
     * @param balance 回收的TRX数量（Sun单位）
     * @param resource 资源类型：0=带宽，1=能量
     * @return 创建的交易对象
     */
    public JsonNode createUndelegateResourceTransaction(String ownerAddress, String receiverAddress,
                                                       long balance, int resource) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/undelegateresource";

        // 确保地址格式正确
        String normalizedOwnerAddress = ensureBase58Address(ownerAddress);
        String normalizedReceiverAddress = ensureBase58Address(receiverAddress);

        log.debug("TRON API: 创建资源回收交易 | owner: {} | receiver: {} | balance: {} Sun | resource: {}",
            normalizedOwnerAddress, normalizedReceiverAddress, balance, resource);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("owner_address", normalizedOwnerAddress);
        requestBody.put("receiver_address", normalizedReceiverAddress);
        requestBody.put("balance", balance);
        requestBody.put("resource", resource);
        requestBody.put("visible", true);

        JsonNode result = executePost(url, requestBody);

        // 验证交易创建结果
        validateTransactionCreationResult(result, "资源回收");

        return result;
    }

    /**
     * 获取带宽价格
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public BigInteger getBandwidthPrice() {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/getbandwidthprices";

        JsonNode result = executeGet(url);
        if (result.has("prices")) {
            String prices = result.get("prices").asText();
            String[] priceEntries = prices.split(",");
            String lastEntry = priceEntries[priceEntries.length - 1];
            String[] parts = lastEntry.split(":");
            return BigInteger.valueOf(Long.parseLong(parts[1]));
        }

        throw new TronBlockException("获取带宽价格失败");
    }



    /**
     * 预估TRC20转账所需能量
     * 使用estimateenergy API来获取准确的能量消耗预估
     *
     * @param fromAddress     发送方地址
     * @param contractAddress 合约地址
     * @param toAddress       接收方地址
     * @param amount          转账金额（原始单位）
     * @return 预估的能量消耗
     */
    @Retryable(retryFor = {Exception.class},
        backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public long estimateEnergy(String fromAddress, String contractAddress, String toAddress, long amount) {
        String url = apiConfig.getAvailableEndpoint() + "/wallet/estimateenergy";

        try {
            // 构造TRC20 transfer方法调用参数
            String toAddressHex = TronAddressUtils.addressToHex(toAddress);
            String amountHex = String.format("%064x", amount);
            String parameter = toAddressHex + amountHex;

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("owner_address", fromAddress);
            requestBody.put("contract_address", contractAddress);
            requestBody.put("function_selector", "transfer(address,uint256)");
            requestBody.put("parameter", parameter);
            requestBody.put("visible", true);

            log.debug("TRON API: 预估TRC20转账能量 | from: {} | to: {} | contract: {} | amount: {}",
                fromAddress, toAddress, contractAddress, amount);

            JsonNode result = executePost(url, requestBody);

            // 解析能量预估结果
            if (result.has("energy_required")) {
                long energyRequired = result.get("energy_required").asLong();
                log.debug("TRON API: 能量预估成功 | 需要能量: {}", energyRequired);
                return energyRequired;
            } else if (result.has("Energy")) {
                long energyRequired = result.get("Energy").asLong();
                log.debug("TRON API: 能量预估成功 | 需要能量: {}", energyRequired);
                return energyRequired;
            }

            // 检查是否有错误信息
            if (result.has("Error")) {
                String error = result.get("Error").asText();
                log.warn("TRON API: 能量预估失败 | error: {}", error);
                throw new TronBlockException("能量预估失败: " + error);
            }

            log.warn("TRON API: 能量预估返回空结果，使用默认值 50000");
            return 50000; // 默认预估值

        } catch (Exception e) {
            log.error("TRC20转账能量预估失败: from={}, contract={}, error={}",
                fromAddress, contractAddress, e.getMessage());
            throw new TronBlockException("能量预估失败: " + e.getMessage());
        }
    }

    /**
     * 估算交易大小（用于带宽计算）
     * 基于交易类型和参数估算交易的字节大小
     *
     * @param transactionType 交易类型（"TRX" 或 "TRC20"）
     * @param fromAddress     发送方地址
     * @param toAddress       接收方地址
     * @param contractAddress 合约地址（TRC20时需要）
     * @return 预估的交易大小（字节）
     */
    public int estimateTransactionSize(String transactionType, String fromAddress, String toAddress, String contractAddress) {
        try {
            int baseSize = 200; // 基础交易大小
            int addressSize = 21; // TRON地址大小
            int signatureSize = 65; // 签名大小

            if ("TRX".equalsIgnoreCase(transactionType)) {
                // TRX转账：基础大小 + 地址 + 签名
                int estimatedSize = baseSize + (addressSize * 2) + signatureSize;
                log.debug("TRX转账大小预估: {} 字节", estimatedSize);
                return estimatedSize;
            } else if ("TRC20".equalsIgnoreCase(transactionType)) {
                // TRC20转账：基础大小 + 地址 + 合约地址 + 方法调用数据 + 签名
                int methodDataSize = 68; // transfer方法数据大小（4字节选择器 + 32字节地址 + 32字节金额）
                int estimatedSize = baseSize + (addressSize * 3) + methodDataSize + signatureSize;
                log.debug("TRC20转账大小预估: {} 字节", estimatedSize);
                return estimatedSize;
            } else {
                log.warn("未知交易类型: {}，使用默认大小", transactionType);
                return 350; // 默认大小
            }

        } catch (Exception e) {
            log.error("交易大小预估失败: type={}, error={}", transactionType, e.getMessage());
            return 350; // 出错时返回保守预估值
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 执行POST请求
     */
    private JsonNode executePost(String url, Object requestBody) {
        try {
            HttpHeaders headers = createHeaders();
            HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                if (responseBody == null || responseBody.trim().isEmpty()) {
                    return objectMapper.createObjectNode();
                }
                return objectMapper.readTree(responseBody);
            } else {
                throw new TronBlockException("HTTP请求失败: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("TRON API: POST请求失败 | url: {} | error: {}", url, e.getMessage());
            throw new TronBlockException("API请求失败: " + e.getMessage());
        }
    }

    /**
     * 执行GET请求
     */
    private JsonNode executeGet(String url) {
        try {
            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                if (responseBody == null) {
                    return objectMapper.createObjectNode();
                }
                return objectMapper.readTree(responseBody);
            } else {
                throw new TronBlockException("HTTP请求失败: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.debug("GET请求失败: {} | {}", url, e.getMessage());
            throw new TronBlockException("API请求失败: " + e.getMessage());
        }
    }

    /**
     * 创建HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("User-Agent", apiConfig.getUserAgent());

        // 只有主网才需要API密钥，测试网（Shasta/Nile）不需要
        if (!apiConfig.isTestnet() && apiConfig.hasApiKeys()) {
            String apiKey = apiConfig.getApiKeys().get(0);
            // 减少API密钥使用的日志频率，避免过于冗余
            headers.set("TRON-PRO-API-KEY", apiKey);
        }

        return headers;
    }

}
