package org.dromara.common.scanning.chain.model;

import org.dromara.common.scanning.chain.model.eth.EthTransactionModel;
import org.dromara.common.scanning.chain.model.tron.TronTransactionModel;



/**
 * Scanned transaction result
 */
public class TransactionModel {

    /**
     * Transaction objects on the Ethereum chain
     */
    private EthTransactionModel ethTransactionModel;

    /**
     * TransactionInfo on the Tron chain
     */
    private TronTransactionModel tronTransactionModel;

    /**
     * Chain type identifier (BSC, ARB, BASE, ETH, TRON, SOL)
     * 用于标识交易所属的区块链类型
     */
    private String chainType;



    // TODO SOL are under development, so there is no result set attribute for the time being

    public static TransactionModel builder(){
        return new TransactionModel();
    }

    public EthTransactionModel getEthTransactionModel() {
        return ethTransactionModel;
    }

    public TransactionModel setEthTransactionModel(EthTransactionModel ethTransactionModel) {
        this.ethTransactionModel = ethTransactionModel;
        return this;
    }

    public TronTransactionModel getTronTransactionModel() {
        return tronTransactionModel;
    }

    public TransactionModel setTronTransactionModel(TronTransactionModel tronTransactionModel) {
        this.tronTransactionModel = tronTransactionModel;
        return this;
    }

    public String getChainType() {
        return chainType;
    }

    public TransactionModel setChainType(String chainType) {
        this.chainType = chainType;
        return this;
    }


}
